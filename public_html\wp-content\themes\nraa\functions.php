<?php
/**
 * NRAA Functions File
 *
 * Setup default values etc.
 *
 * For more information on hooks, actions, and filters, see http://codex.wordpress.org/Plugin_API.
 *
 * @package    WordPress
 * @subpackage NRAA
 * @license    GPLv2 or later http://www.gnu.org/licenses/gpl-2.0.html
 */
// Easier to set this on local dev than use the URL...
// URL can be used for production debugging
//$_GET['_debug'] = 'errors';
if (isset($_GET['_debug']) && strpos($_GET['_debug'], 'errors') !== FALSE) {
  error_reporting(E_ALL);
  ini_set('display_errors', 1);
}
ini_set('memory_limit', '512M');
ini_set('max_execution_time', 90);

define('SHORTNAME', 'nraa');    // Used for option settings etc
define('VERSION', 2.340);     // Version for refreshing caches
define('MAX_SHOOTS', get_option('nraa_max_shoots', 8));        // Maximum number of shoots usable for grading algorithm
define('MIN_SHOOTS', get_option('nraa_min_shoots', 3));        // Minimum number of shoots usable for grading algorithm
define('GRADING_START_DATE', '4 years ago'); // Starting date for the grading algorithm

// Ensure the timezone is set
date_default_timezone_set('Australia/Canberra');

// Include non-wordpress specific functions/methods
require __DIR__ . '/vendor/autoload.php';
require __DIR__ . '/includes/functions.php';

// Admin pages
require 'admin/site-settings.php';
require 'admin/shortcodes.php';
require 'admin/ajax.php';

/**
 * NRAA Wordpress Functions
 */
class NRAA
{
  /** @var array Australian States */
  protected $aus_states = array(
    'ACT' => 'Australian Capital Territory',
    'NSW' => 'New South Wales',
    'VIC' => 'Victoria',
    'QLD' => 'Queensland',
    'SA' => 'South Australia',
    'WA' => 'Western Australia',
    'TAS' => 'Tasmania',
    'NT' => 'Northern Territory'
  );
  /** @var array Calendar States */
  protected $calendar_states = array(
    'NRAA' => 'National Rifle Association of Australia',
    'NSW' => 'New South Wales',
    'ACT' => 'Australian Capital Territory',
    'VIC' => 'Victoria',
    'TAS' => 'Tasmania',
    'SA' => 'South Australia',
    'WA' => 'Western Australia',
    'NT' => 'Northern Territory',
    'QLD' => 'Queensland',
    'NQLD' => 'North Queensland'
  );
  /** @var array Custom Modules */
  protected $_modules = array();

  public function __get($name)
  {
    if (isset($this->$name)) {
      return $this->$name;
    }

    return null;
  }

  /**
   * Initialize all the NRAA WordPress customisation
   */
  public function init()
  {
    $this->register_scripts()
      ->register_styles();

    add_filter('black_studio_touch_dropdown_menu_selector', function ($selector) {
      return '.nav a';
    });
    add_action('add_meta_boxes', function () {
      add_meta_box('nraa_shortcodes', __('NRAA Shortcode Reference'), function ($post) {
        get_template_part('templates/admin', 'shortcodes');
      }, 'page', 'side', 'low');
    });
    add_action('init', 'nraa_register_shortcodes');

    // Translations
    $translations_dir = get_template_directory() . DIRECTORY_SEPARATOR . 'translations' . DIRECTORY_SEPARATOR;
    load_plugin_textdomain('properties', $translations_dir, null);

    // Add custom background support
    add_theme_support('custom-background', array(
      'default-color' => 'e3e4e6',
      'default-image' => get_template_directory_uri() . '/images/bg.png',
      'default-repeat' => 'no-repeat',
      'wp-head-callback' => '_custom_background_cb',
    ));

    // Add custom header support
    add_theme_support('custom-header', array(
      'default-image' => get_template_directory_uri() . '/images/header.png',
      'random-default' => false,
      'width' => 1000,
      'height' => 118,
      'flex-height' => false,
      'flex-width' => false,
      'default-text-color' => '',
      'header-text' => false,
      'uploads' => true,
      'wp-head-callback' => '',
      'admin-head-callback' => '',
      'admin-preview-callback' => '',
    ));

    // Add sidebar support
    if (function_exists('register_sidebar')) {
      register_sidebar(array(
        'name' => 'Footer Links',
        'before_widget' => '<section class="%1$s">',
        'after_widget' => '</section>',
        'before_title' => '<h4>',
        'after_title' => '</h4>'
      ));
    }
    // Add support for custom menus
    if (function_exists('register_nav_menu')) {
      register_nav_menu('primary-menu', __('Primary Menu'));
    }

    // Add additional css and javascript
    add_action('wp_enqueue_scripts', function () {
      global $post;
      wp_localize_script('nraa-scripts', 'NraaAjax', array('ajaxurl' => admin_url('admin-ajax.php')));

      wp_deregister_script('jquery');
      wp_deregister_script('jquery-migrate');
      wp_enqueue_script('debug-scripts');
      wp_enqueue_script('modernizr');
      wp_enqueue_script('jquery-cdn');
      wp_enqueue_script('jquery-migrate-cdn');
      wp_enqueue_script('jquery-ui-cdn');
      wp_enqueue_script('jquery-fallback');
      wp_enqueue_script('jquery-validate');
      wp_enqueue_script('underscorejs');
      wp_enqueue_script('nraa-scripts');

      if (is_page_template('templates/entry-form.php')) {
        wp_enqueue_script('knockoutjs');
        wp_enqueue_script('entry-form-scripts');
      }
      else if (is_page_template('templates/a-grade-rankings.php')
        || is_page_template('templates/other-grade-rankings.php')
        || (isset($post) && is_object($post) && isset($post->post_content) && has_shortcode($post->post_content, 'sid-list'))) {
        wp_enqueue_script('nraa-rankings-scripts');
        wp_enqueue_style('nraa-rankings-styles');
      }

      wp_enqueue_style('jquery-ui-styles');
      wp_enqueue_style('nraa-fonts');
      wp_enqueue_style('nraa-styles');
    });

    // Custom media filters
    add_filter('post_mime_types', function () {
      $post_mime_types = array( //  array( adj, noun )
        'image' => array(__('Images'), __('Manage Images'), _n_noop('Image <span class="count">(%s)</span>', 'Images <span class="count">(%s)</span>')),
        'audio' => array(__('Audio'), __('Manage Audio'), _n_noop('Audio <span class="count">(%s)</span>', 'Audio <span class="count">(%s)</span>')),
        'video' => array(__('Video'), __('Manage Video'), _n_noop('Video <span class="count">(%s)</span>', 'Video <span class="count">(%s)</span>')),
        'application/msword' => array(__('DOCs'), __('Manage DOCs'), _n_noop('DOC <span class="count">(%s)</span>', 'DOC <span class="count">(%s)</span>')),
        'application/vnd.ms-excel' => array(__('XLSs'), __('Manage XLSs'), _n_noop('XLS <span class="count">(%s)</span>', 'XLSs <span class="count">(%s)</span>')),
        'application/pdf' => array(__('PDFs'), __('Manage PDFs'), _n_noop('PDF <span class="count">(%s)</span>', 'PDFs <span class="count">(%s)</span>'))
      );

      return $post_mime_types;
    });
    add_filter('gettext', function ($translated, $text = false, $domain = false) {
      if ($translated == 'Howdy, %1$s') {
        $translated = 'G\'day, %1$s';
      }

      return stripslashes($translated);
    });
    add_filter('widget_text', 'do_shortcode');

    /*
     * Customise the login page
     */
    add_filter('login_redirect', function ($redirect_to, $request, $user) {
      return $redirect_to;
    }, 10, 3);
    add_filter('login_headerurl', 'get_site_url');
    add_action('login_head', function () {
      wp_enqueue_script('jquery-cdn');
      wp_enqueue_script('nraa-login-scripts');
      wp_enqueue_style('nraa-login-styles');
    });

    /*
     * Customize the administration
     */
    add_action('wp_before_admin_bar_render', function () {
      if (!current_user_can('read')) {
        add_filter('show_admin_bar', '__return_false');
      }

      global $wp_admin_bar;
      $wp_admin_bar->remove_node('wp-logo');
      $wp_admin_bar->remove_node('comments');

      // Add a quick link to the admin section
      if (is_admin()) {
        NraaMenuBar::getInstance()->add_root_menu(SHORTNAME . '_site_shortcut', 'Frontend', get_home_url());
      } else {
        NraaMenuBar::getInstance()->add_root_menu(SHORTNAME . '_admin_shortcut', 'Backend', get_admin_url());
      }
    });

    // Non-admin AJAX methods
    add_action('wp_ajax_get', 'nraa_ajax_get');
    add_action('wp_ajax_get-entries-for-match', 'nraa_ajax_get_entries_for_match');
    add_action('wp_ajax_get-shooter-details', 'nraa_ajax_get_shooter_details');
    add_action('wp_ajax_nopriv_get-entries-for-match', 'nraa_ajax_get_entries_for_match');
    add_action('wp_ajax_entry-form', 'nraa_ajax_entry_form');
    add_action('wp_ajax_nopriv_entry-form', 'nraa_ajax_entry_form');
    add_action('wp_ajax_is-already-entered', 'nraa_ajax_is_already_entered');
    add_action('wp_ajax_nopriv_is-already-entered', 'nraa_ajax_is_already_entered');
    add_action('wp_ajax_get-entered-shooters', 'nraa_ajax_get_entered_shooters');
    add_action('wp_ajax_nopriv_get-entered-shooters', 'nraa_ajax_get_entered_shooters');
    add_action('wp_ajax_guess-shooter-grade', 'nraa_ajax_guess_shooter_grade');
    add_action('wp_ajax_nopriv_guess-shooter-grade', 'nraa_ajax_guess_shooter_grade');
    add_action('wp_ajax_grading-details', 'nraa_ajax_grading_details');
    add_action('wp_ajax_nopriv_grading-details', 'nraa_ajax_grading_details');
    add_action('wp_ajax_get-calculated-grades', 'nraa_ajax_get_calculated_grades');
    add_action('wp_ajax_nopriv_get-calculated-grades', 'nraa_ajax_get_calculated_grades');
    add_action('wp_ajax_upload-hexta', 'nraa_ajax_upload_hexta');
    add_action('wp_ajax_perform-calcs', 'nraa_ajax_perform_calcs');
    add_action('wp_ajax_read-queue', 'nraa_ajax_read_queue');

    if (is_admin()) {
      add_action('admin_init', function () {
        remove_menu_page('link-manager.php');
        remove_menu_page('edit-comments.php');

        // Separate before NRAA management stuff
        NRAA::add_admin_menu_separator(102);
      });

      // Admin AJAX methods
      add_action('wp_ajax_get-shooter-grade', 'nraa_ajax_get_shooter_grade');

      // Remove unnecessary dashboard widgets
      add_action('wp_dashboard_setup', function () {
        remove_meta_box('dashboard_recent_comments', 'dashboard', 'normal');
        remove_meta_box('dashboard_incoming_links', 'dashboard', 'normal');
        remove_meta_box('dashboard_plugins', 'dashboard', 'normal');
        remove_meta_box('dashboard_quick_press', 'dashboard', 'side');
        remove_meta_box('dashboard_recent_drafts', 'dashboard', 'side');
        remove_meta_box('dashboard_primary', 'dashboard', 'side');
        remove_meta_box('dashboard_secondary', 'dashboard', 'side');
      });

      add_action('admin_enqueue_scripts', function () {
        wp_enqueue_style('nraa-admin-styles');
        wp_enqueue_script('underscorejs');
      });

      add_action('wp_before_admin_bar_render', function () {
        NraaMenuBar::getInstance()->add_root_menu(SHORTNAME . '_management', 'Management');
        NraaMenuBar::getInstance()->add_root_menu(SHORTNAME . '_configuration', 'Configuration');
      });

      // Modules
      foreach ($this->_modules as $module) {
        $module->init();
      }
    }

    add_action('shutdown', function () {
      foreach (get_debug_messages() as $message)
        echo $message;
    });
  }

  protected function register_styles()
  {
    $theme = session_get(SHORTNAME . '_jquery_ui_theme', function () {
      return get_option(SHORTNAME . '_jquery_ui_theme', 'pepper-grinder');
    });

    wp_register_style('nraa-fonts', 'https://fonts.googleapis.com/css?family=Droid+Sans', false);
    wp_register_style('jquery-ui-styles', "https://ajax.googleapis.com/ajax/libs/jqueryui/1.11.4/themes/$theme/jquery-ui.min.css", false, '1.11.4', 'all');
    wp_register_style('chosen', 'https://cdnjs.cloudflare.com/ajax/libs/chosen/0.9.12/chosen.min.css', false, '0.9.12', 'all');
    wp_register_style('twitter-bootstrap-cdn', 'https://cdnjs.cloudflare.com/ajax/libs/twitter-bootstrap/3.3.4/css/bootstrap.min.css', false, '3.3.4', 'all');
    wp_register_style('nraa-styles', get_stylesheet_uri(), array("nraa-fonts"), VERSION, 'all');
    wp_register_style('nraa-login-styles', get_stylesheet_directory_uri() . '/css/dist/login.min.css', false, VERSION, 'all');
    wp_register_style('nraa-admin-styles', get_stylesheet_directory_uri() . '/css/dist/admin.min.css', false, VERSION, 'all');
    wp_register_style('nraa-report-styles', get_stylesheet_directory_uri() . '/css/dist/reports.min.css', false, VERSION, 'all');
    wp_register_style('nraa-rankings-styles', get_stylesheet_directory_uri() . '/css/dist/rankings.min.css', false, VERSION, 'all');

    return $this;
  }

  protected function register_scripts()
  {
    wp_register_script('modernizr', 'https://cdnjs.cloudflare.com/ajax/libs/modernizr/2.6.2/modernizr.min.js', false);
    wp_register_script('jquery-cdn', 'https://cdnjs.cloudflare.com/ajax/libs/jquery/2.2.4/jquery.min.js', false, '2.2.4', false);
    wp_register_script('jquery-migrate-cdn', 'https://cdnjs.cloudflare.com/ajax/libs/jquery-migrate/1.4.1/jquery-migrate.min.js', array('jquery-cdn'), '1.4.1', false);
    wp_register_script('jquery-ui-cdn', 'https://cdnjs.cloudflare.com/ajax/libs/jqueryui/1.11.4/jquery-ui.min.js', array('jquery-migrate-cdn'), '1.11.4', false);
    wp_register_script('jquery-validate', 'https://cdnjs.cloudflare.com/ajax/libs/jquery-validate/1.11.1/jquery.validate.min.js', array('jquery-cdn'), '1.11.1', true);
    wp_register_script('twitter-bootstrap-cdn', 'https://cdnjs.cloudflare.com/ajax/libs/twitter-bootstrap/3.3.4/js/bootstrap.min.js', array('jquery-cdn'), '3.3.4', true);
    wp_register_script('knockoutjs', 'https://cdnjs.cloudflare.com/ajax/libs/knockout/2.2.1/knockout-min.js', array('jquery-cdn'), '2.2.1', false);
    wp_register_script('knockoutjs-mapping', 'https://cdnjs.cloudflare.com/ajax/libs/knockout.mapping/2.3.5/knockout.mapping.min.js', array('jquery-cdn', 'knockoutjs'), '2.3.5', false);
    wp_register_script('underscorejs', 'https://cdnjs.cloudflare.com/ajax/libs/underscore.js/1.8.3/underscore-min.js', false, '1.8.3', false);
    wp_register_script('chosen', 'https://cdnjs.cloudflare.com/ajax/libs/chosen/0.9.12/chosen.jquery.min.js', array('jquery-cdn', 'jquery-ui-cdn'), '0.9.12', true);
    wp_register_script('nraa-login-scripts', get_stylesheet_directory_uri() . '/js/dist/login.min.js', false, VERSION, true);
    wp_register_script('nraa-scripts', get_stylesheet_directory_uri() . '/js/dist/scripts.min.js', false, VERSION, true);
    wp_register_script('nraa-admin-scripts', get_stylesheet_directory_uri() . '/js/dist/admin.min.js', false, VERSION, true);
    wp_register_script('nraa-admin-matches', get_stylesheet_directory_uri() . '/js/dist/matches.min.js', false, VERSION, true);
    wp_register_script('entry-form-scripts', get_stylesheet_directory_uri() . '/js/dist/entry-form.min.js', array('jquery-cdn', 'jquery-ui-cdn', 'nraa-scripts'), VERSION, true);
    wp_register_script('event-edit', get_stylesheet_directory_uri() . '/js/dist/event-edit.min.js', array('jquery-cdn'), VERSION, true);
    wp_register_script('nraa-rankings-scripts', get_stylesheet_directory_uri() . '/js/dist/rankings.min.js', array('jquery-cdn', 'jquery-ui-cdn', 'nraa-scripts'), VERSION, true);

    return $this;
  }

  public static function add_admin_menu_separator($position)
  {
    global $menu;
    if ($menu === null)
      return;

    $index = 0;
    foreach ($menu as $offset => $section) {
      if (substr($section[2], 0, 9) == 'separator')
        $index++;
      if ($offset >= $position) {
        if (isset($menu[$position])) {
          $menu_next = $menu[$position];
          for ($i = $position + 1; ; $i++) {
            if (isset($menu[$i])) {
              $temp = $menu[$i];
              $menu[$i] = $menu_next;
              $menu_next = $temp;
            } else {
              $menu[$i] = $menu_next;
              break;
            }
          }
        }
        $menu[$position] = array('', 'read', "separator{$index}", '', 'wp-menu-separator');
        break;
      }
    }
    ksort($menu);
  }

  /**
   * Add in a custom module
   *
   * @chainable
   * @param  IModule $module
   * @return NRAA
   */
  public function addModule($module)
  {
    if (!is_array($this->_modules)) {
      $this->_modules = array();
    }

    $this->_modules[get_class($module)] = $module;

    return $this;
  }

  public function getModule($module_name)
  {
    return $this->_modules[$module_name];
  }
}

global $nraa;
$nraa = new NRAA();
$nraa->addModule(new ShooterManagementModule())
  ->addModule(new EventManagementModule())
  ->addModule(new TeamManagementModule())
  ->addModule(new MatchManagementModule())
  ->addModule(new ResultManagementModule())
  ->addModule(new ConfigurationsModule())
  ->addModule(new DisciplineConfigurationModule())
  ->addModule(new GradeConfigurationModule())
  ->addModule(new LinkConfigurationModule())
  ->addModule(new DivisionConfigurationModule())
  ->addModule(new RangeDurationConfigurationModule())
  ->addModule(new ReportsModule());

$nraa->init();

// Fix SQL syntax issue where 'all' is used instead of '*' in SELECT queries
add_filter('posts_fields', function($fields, $query) {
  // Fix the specific issue where 'all' is used instead of '*'
  if ($fields === 'all') {
    global $wpdb;
    $fields = "{$wpdb->posts}.*";
  }
  return $fields;
}, 10, 2);

// Additional fix for NextGen Gallery datamapper SQL syntax issue
add_filter('posts_request', function($sql, $query) {
  // Fix the SQL_CALC_FOUND_ROWS all issue in NextGen Gallery
  if (strpos($sql, 'SELECT SQL_CALC_FOUND_ROWS  all') !== false) {
    global $wpdb;
    $sql = str_replace('SELECT SQL_CALC_FOUND_ROWS  all', "SELECT SQL_CALC_FOUND_ROWS {$wpdb->posts}.*", $sql);
  }
  return $sql;
}, 10, 2);
