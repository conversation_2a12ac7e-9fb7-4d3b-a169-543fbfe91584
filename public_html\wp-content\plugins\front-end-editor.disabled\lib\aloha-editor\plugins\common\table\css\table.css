/* reset */
.aloha-table tr.aloha-table-selectcolumn {
	line-height: 0.01em;
	height: 12px !important;
}
.aloha-table td.aloha-table-selectrow {
	width: 25px !important;
}
.aloha-editable div.aloha-table-wrapper, .aloha-table-wrapper div.aloha-table-cell-editable, 
.aloha-table-wrapper .aloha-table-leftuppercorner div, .aloha-table-wrapper tr.aloha-table-selectcolumn td,
.aloha-table-wrapper td.aloha-table-selectrow , .aloha-table-wrapper div.aloha-editable-caption { 
	margin:0; 
	padding:0;
	border:none; 
	background:none;
	outline: 0px solid transparent;
	zoom:1;
}

/* editable wrappers in cells */
div.aloha-table-cell-editable {
	height: 100%;
	min-height: 100%;
	width: 100%;
	z-index: 99999;
}

/* only show controls when in active state */
tr.aloha-table-selectcolumn td,
td.aloha-table-selectrow {
	visibility: hidden;
}
.aloha-editable-active tr.aloha-table-selectcolumn td,
.aloha-editable-active td.aloha-table-selectrow {
	visibility: visible;
}

/* selection and hover */
.aloha-cell-selected {
	background-color: #80B5F2 !important;
	color: #FFF;
}
.aloha-table td.aloha-table-selectrow,
.aloha-table tr.aloha-table-selectcolumn td {
	background-color: #D0D0D0;
}
.aloha-table td.aloha-table-selectrow:hover,
.aloha-table tr.aloha-table-selectcolumn td:hover {
	background-color: #FFE767;
}


/* TODO make custom cursors visible in IE */
.aloha-table tr.aloha-table-selectcolumn td.aloha-table-leftuppercorner,
.aloha-table tr.aloha-table-selectcolumn td.aloha-table-leftuppercorner:hover {
	cursor: default;
	background-color: transparent;
}
.aloha-table td .aloha-table-cell-editable {
	cursor: text !important;
}
.aloha-table tr.aloha-table-selectcolumn td {
	cursor: url(../img/down.cur),s-resize;
}
.aloha-table td.aloha-table-selectrow {
	cursor: url(../img/left.cur),e-resize;
}

/* wai indicator */
.aloha-table-wrapper div.aloha-wai-red {
	background-image: url(../img/wai-red.png);
}

.aloha-table-wrapper div.aloha-wai-green {
	background-image: url(../img/wai-green.png);
}

.aloha-table td.aloha-table-leftuppercorner div {
	cursor: pointer !important;
}

/* create dialog */
.aloha-table-createdialog {
	z-index: 99999;
	position: absolute;
	background-color: #F0F0F0;
	border: 1px solid #cccccc;
	-moz-border-radius: 2px;
	-webkit-border-radius: 2px;
	border-radius: 2px;
}
.aloha-table-createdialog table {
	padding: 0px;
	margin: 0px;
}
.aloha-table-createdialog table td {
	margin: 0px;
	padding: 0px;
	border: 1px solid #AFAFAF;
	font-size: 8px;
	width: 15px;
	height: 15px;
}
.aloha-table-createdialog table td.hover {
	background-color: #1c94c4;
}
.aloha-table-createdialog #table-size-info {
	text-align: center;
	font-size: 0.8em;
}

.aloha-table-textarea {
	width:90%;
	height:100px;
	margin-left:5px;
	margin-right:5px;
}

.aloha-table-label {
	margin-left:5px;
}

.aloha .ui-state-default .aloha-icon-table-naturalfit {
	background-position: -432px 0px;
}
