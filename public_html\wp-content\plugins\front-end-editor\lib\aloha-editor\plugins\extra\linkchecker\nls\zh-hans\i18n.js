define({
	"error.0": "检查URL时发生错误。",
	"error.400": "错误的请求。由于包含语法错误，当前请求无法被服务器理解。",
	"error.401": "未经授权。当前请求需要用户验证。",
	"error.402": "需要付费。",
	"error.403": "禁止访问。服务器已经理解请求，但是拒绝执行它。",
	"error.404": "请求失败。请求所希望得到的资源未被在服务器上发现。",
	"error.405": "请求的方法不被允许。",
	"error.406": "请求不能接受。你的浏览器不支持此内容。",
	"error.407": "需要在代理服务器上进行身份验证。",
	"error.408": "请求超时。服务器等待发送请求时超时。",
	"error.409": "请求出现冲突。",
	"error.410": "被请求的资源在服务器上已经不再可用。",
	"error.411": "服务器要求提供长度。这个链接可能会在浏览器中工作。",
	"error.412": "前提条件失败。这个链接可能会在浏览器中工作。",
	"error.413": "请求实体过大。请求提交的实体数据大小超过了服务器愿意或者能够处理的范围。",
	"error.414": "请求URL太长。请求的URI长度超过了服务器能够解释的长度。",
	"error.415": "不支持的媒体类型。该服务器或资源不支持请求实体的媒体类型。",
	"error.416": "请求的范围无法满足。客户端要求文件的一部分，但服务器无法提供的那部分。",
	"error.417": "预期失败。服务器不能满足您的浏览器的要求。",
	"error.418": "我是咖啡壶。;-)",
	"error.500": "服务器内部错误。一般性错误消息，没有更具体的消息是适合。",
	"error.501": "没有落实。服务器无法识别请求方法，或缺乏履行要求的能力。",
	"error.502": "错误的网关。服务器作为网关或代理，从上游服务器收到无效响应。",
	"error.503": "服务不可用。服务器目前无法使用（因为它超载或关闭进行维修）。一般来说，这是一个临时的状态。",
	"error.504": "网关超时。服务器作为网关或代理，并没有从上游服务器得到及时的反应。",
	"error.505": "HTTP版本不受支持。服务器不支持请求中使用的HTTP协议版本。"
});
