define({
	"error.0": "Се појави грешка при проверувањето на оваа адреса.",
	"error.400": "Лошо барање. Барањето не можеше да биде извршено поради лоша синтакса.",
	"error.401": "Неавторизиран. Автентикацијата не беше успешна или сеуште не е поднесена.",
	"error.402": "Потребно е плаќање.",
	"error.403": "Забрането. Барањето беше легитимно, но серверот одбива да одговори.",
	"error.404": "Не е најдено. Бараниот ресурс не беше најден, но можеби ќе е достапен во иднина.",
	"error.405": "Методот не е дозволен.",
	"error.406": "Неприфатливо. Твојот пребарувач не ја поддржува содржината.",
	"error.407": "Потребна е автентикација на посредувач.",
	"error.408": "Барањето го помина дозволеното време. Серверот се откажа чекајки го барањето.",
	"error.409": "Конфликт во барањето.",
	"error.410": "Ресурсот го нема.",
	"error.411": "Должината е барана од серверот. Овој линк можеби ќе работи во пребарувачот.",
	"error.412": "Претходен услов не е стекнат. Овојо линк можеби ќе работи во пребарувачот.",
	"error.413": "Барањето е премногу големо. Ова барање е поголемо отколку што серверот може или сака да обработи.",
	"error.414": "Адресата на барањето е премногу долга за серверот да го обработи.",
	"error.415": "Овој тип на содржина не е поддржан. Барањето е составено од содржина која не е поддржана од серверот.",
	"error.416": "Бараниот опсег не е достапен. Клиентот побара дел од датотека, но серверот не може да го обезбеди тој дел.",
	"error.417": "Очекувањето на беше сретнато. Серверот не може да ги сретне очекувањата од твојот пребарувач.",
	"error.418": "Јас сум чајник. ;-)",
	"error.500": "Внатрешна грешка на серверот. Оваа порака се прикажува кога ниедна друга порака не е погодна.",
	"error.501": "Не е имплементирано. Серверот или не го препознава методот на барањето, или не е во можност да го опслужи.",
	"error.502": "Лош портал. Серверот се однесува како портал или посредник и прими невалидно барање од серверот.",
	"error.503": "Услугата е недостапна. Серверот моментално не е во можност (поради преоптеретување или поради одржување). Обично, ова е привремено.",
	"error.504": "Серверот се однесуваше како портал или посредник и не доби навремен одговор од главниот сервер.",
	"error.505": "Оваа верзија на HTTP протоколот не е поддржана. Серверот не ја поддржува верзијата на HTTP искористена во ова барање."
});
