define({
	"button.bold.label": "Negrito",
	"button.italic.label": "Itálico",
	"button.strikethrough.label": "Tachado",
	"button.subscript.label": "Subscrito",
	"button.superscript.label": "Sobrescrito",
	"button.underline.label": "<PERSON>linhado",
	"button.yes.label": "Sim",
	"button.no.label": "Não",
	"button.p.label": "Parágrafo",
	"button.h1.label": "Cabeçalho 1",
	"button.h2.label": "Cabeçalho 2",
	"button.h3.label": "Cabeçalho 3",
	"button.h4.label": "Cabeçalho 4",
	"button.h5.label": "Cabeçalho 5",
	"button.h6.label": "Cabeçalho 6",
	"button.pre.label": "Texto pré-formatado",
	"button.removeFormatting.label": "Remover formatação",
	"button.ol.label": "Insirir lista ordenada",
	"button.ul.label": "Insirir lista desordenada",
	"button.indent.label": "Recuar lista",
	"button.outdent.label": "Recuar lista",
	"button.createLink.label": "Inserir link",
	"button.removeLink.label": "Remover link",
	"button.createAbbr.label": "Inserir abreviação",
	"button.characterPicker.label": "Escolher caracteres especiais",
	"button.justifyLeft.label": "Alinhar a esquerda",
	"button.justifyRight.label": "Alinhar a direita",
	"button.justifyCenter.label": "Centralizar",
	"button.justifyFull.label": "Justificar",
	"button.horizontalRule.label": "Inserir régua horizontal",
	"button.createLanguageAnnotation.label": "Inserir anotação de linguagem",
	"button.metaview.label": "Alternar entre meta e normal",
	"button.quote.label": "Formatar seleção como citação",
	"button.blockquote.label": "Formatar seleção como citação em bloco",
	"tab.format.label": "Formatar",
	"tab.insert.label": "Inserir",
	"tab.abbr.label": "Abreviação",
	"tab.img.label": "Imagem",
	"tab.link.label": "Link",
	"tab.list.label": "Lista",
	"tab.table.label": "Tabela",
	"tab.col.label": "Coluna de Tabela",
	"tab.row.label": "Linha da Tabela",
	"tab.wai-lang.label": "Anotação da lingua"
});
