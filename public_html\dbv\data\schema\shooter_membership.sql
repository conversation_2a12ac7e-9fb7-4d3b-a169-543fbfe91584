CREATE TABLE `shooter_membership` (
  `shooter_id` int(10) unsigned NOT NULL,
  `association_id` int(10) unsigned NOT NULL,
  `membership_no` varchar(255) DEFAULT NULL,
  `joined_date` int(11) NOT NULL,
  `expiry_date` int(11) NOT NULL,
  `license_number` varchar(255) DEFAULT NULL COMMENT 'Shooters License Number',
  `license_expiry_date` int(11) DEFAULT NULL,
  `has_atr_magazine` tinyint(1) NOT NULL DEFAULT '0',
  `notes` text,
  PRIMARY KEY (`shooter_id`),
  KEY `association_id` (`association_id`),
  CONSTRAINT `shooter_membership_ibfk_3` FOREIGN KEY (`shooter_id`) REFERENCES `shooters` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
  CONSTRAINT `shooter_membership_ibfk_4` FOREIGN KEY (`association_id`) REFERENCES `associations` (`id`) ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=latin1