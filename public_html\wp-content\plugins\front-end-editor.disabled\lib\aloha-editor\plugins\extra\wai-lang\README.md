# Aloha WAI Lang Plugin

This plugin detects wai language markup and enables the user to mark certain elements for a specific language (in case the element language deviates from the 'default' language'). The available languages are configurable in the plugin configuration.
Elements will be marked by adding the "href lang" attribute.

TODO:
* Suggest languages to the user via DropDown or shortnotation of the plugin.
* Marked elements must be visible to the user
* Howto mark longer sections and make it visible to the user.
* Similiar to abbr()
* check for a valid value if possible -- now also abcd would be valid