.aloha-link-pointer a {
	cursor: pointer;
}

.aloha-link-text {
	cursor: text;
}

.aloha-editable-active .aloha-block.aloha-link-text,
.aloha-block.aloha-block-highlighted.aloha-link-text {
	box-shadow: none;
}

.aloha-link-radioTarget {
	width:auto !important;
}

.aloha-link-title-container {
	padding:5px 5px;
}

.aloha-link-target-container {
	padding:5px 5px;
}

.aloha-link-target-container ul {
	margin-left: 0px;
}

.aloha-link-target-container li {
	padding:0 !important;
	list-style: none;
}

.aloha-link-target-container fieldset {
	border: 2px groove threedface !important;
	padding-bottom: 5px;
}

.aloha-link-title-container fieldset {
	border: 2px groove threedface !important;
	padding-bottom: 5px;
}

.aloha-link-target-container fieldset input[type=text], .aloha-link-title-container fieldset input[type=text] {
	width: 90%;
}

.x-form-field.x-form-text.aloha-link-href-field {
	color: #333 !important;
	padding: 3px;
}
