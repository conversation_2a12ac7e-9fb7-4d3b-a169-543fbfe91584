DROP EVENT IF EXISTS grading_algorithm_event;
CREATE EVENT grading_algorithm_event
  ON SCHEDULE EVERY 24 HOUR STARTS '2014-03-04 23:19:20'
DO BEGIN
  SET @num := 0, @type := 0;

  DELETE FROM shooter_calculated_grades;

  INSERT INTO shooter_calculated_grades (shooter_id, score, grade, discipline_id, discipline_name)
-- Why are we saving the discipline name in this table? is there a reason for this de-normalization?
    SELECT s.id, SUM(grd.pctscore) / COUNT(*) AS Score, GetGrade(SUM(grd.pctscore) / COUNT(*), d.id) AS Grade, d.id, d.name AS discipline
    FROM results r
      JOIN grades g       ON r.grade_id = g.id
      JOIN shooters s     ON r.shooter_id = s.id
      JOIN matches m      ON r.match_id = m.id
      JOIN events e       ON m.event_id = e.id
      JOIN disciplines d  ON discipline_id = d.id
      JOIN (
             SELECT DISTINCT r.shooter_id, s.sid, discipline_id,
               DATE_FORMAT(FROM_UNIXTIME(e.end_date), '%e %b %Y') as EventEndDate,
               r.match_id,
               CombineScore(score_whole, score_partial) AS Score,
               ts.TopScore,
               CombineScore(score_whole, score_partial) / ts.topscore * 100 AS PctScore,
               g.name AS grade,
               d.name AS discipline,
               @num := IF(@type = shooter_id, @num + 1, 1) AS row_number,
               @type := shooter_id AS dummy
             FROM (SELECT * FROM results ORDER BY shooter_id, id DESC) r
               JOIN grades g       ON r.grade_id = g.id
               JOIN shooters s     ON r.shooter_id = s.id
               JOIN matches m      ON r.match_id = m.id
               JOIN events e       ON m.event_id = e.id
               JOIN disciplines d  ON discipline_id = d.id
               JOIN (
                      SELECT match_id, grade_id, MAX(CombineScore(score_whole, score_partial)) AS TopScore
                      FROM results r
                        JOIN grades g   ON r.grade_id = g.id
                        JOIN matches m  ON r.match_id = m.id
                        JOIN events e   ON m.event_id = e.id
                      WHERE m.is_graded = 1
                            AND FROM_UNIXTIME(e.end_date) >= DATE_SUB(NOW(), INTERVAL 2 YEAR)
                            AND e.is_team_event = 0
                      GROUP BY match_id, grade_id
                      ORDER BY r.shooter_id
                    ) ts ON (r.match_id = ts.match_id AND ts.grade_id = r.grade_id)
             WHERE m.is_graded = 1
                   AND FROM_UNIXTIME(e.end_date) >= DATE_SUB(NOW(), INTERVAL 2 YEAR)
                   AND e.is_team_event = 0
             ORDER BY r.shooter_id
           ) grd ON (grd.shooter_id = s.id AND grd.discipline_id = d.id)
    WHERE row_number <= 8
    GROUP BY d.id, s.id
    ORDER BY s.id;

  UPDATE shooter_calculated_grades scg
  SET number_of_shoots = (
    SELECT COUNT(*)
    FROM (SELECT * FROM results ORDER BY shooter_id, id DESC) r
      JOIN grades g       ON r.grade_id = g.id
      JOIN shooters s     ON r.shooter_id = s.id
      JOIN matches m      ON r.match_id = m.id
      JOIN events e       ON m.event_id = e.id
      JOIN disciplines d  ON discipline_id = d.id
    WHERE m.is_graded = 1
          AND FROM_UNIXTIME(e.end_date) >= DATE_SUB(NOW(), INTERVAL 2 YEAR)
          AND e.is_team_event = 0
          AND s.id = scg.shooter_id
          AND d.id = scg.discipline_id
    GROUP BY s.id, discipline_id
  );
END;
