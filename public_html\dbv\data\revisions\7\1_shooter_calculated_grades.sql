DROP TABLE IF EXISTS `shooter_calculated_grades`;

CREATE TABLE `shooter_calculated_grades` (
  `id` int unsigned NOT NULL AUTO_INCREMENT PRIMARY KEY,
  `shooter_id` int(10) unsigned NOT NULL,
  `grade_id` int(10) unsigned NOT NULL,
  `avg_score` decimal(10,5) NOT NULL,
  `number_of_shoots` int NOT NULL,
  FOREIGN KEY (`shooter_id`) REFERENCES `shooters` (`id`),
  FOREIGN KEY (`grade_id`) REFERENCES `grades` (`id`)
);
