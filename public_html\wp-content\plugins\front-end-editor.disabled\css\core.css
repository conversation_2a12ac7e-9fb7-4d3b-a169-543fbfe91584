/* Clearfix */
.fee-clearfix:after {
	visibility: hidden;
	display: block;
	font-size: 0;
	content: " ";
	clear: both;
	height: 0;
}
* html .fee-clearfix             { zoom: 1; } /* IE6 */
*:first-child+html .fee-clearfix { zoom: 1; } /* IE7 */

/* Main */
.fee-field,
.fee-form {
	margin: 0 !important;
	padding: 0 !important;
	border-width: 0 !important;
}

.fee-disabled {
	visibility: collapse;
}

div.fee-form,
.widget_text.fee-group input,
textarea.fee-form-content {
	-moz-box-sizing: border-box;
	-ms-box-sizing: border-box;
	-webkit-box-sizing: border-box;
	box-sizing: border-box;
	clear: both !important;
	display: block !important;
	width: 100% !important;
}

.fee-loading {
	position: absolute;
	z-index: 9999;
	background-position: center center;
	background-repeat: no-repeat;
	text-align: center;
	opacity: 0.75;
}

.fee-error {
	background-color: #C43;
	color: #FFF;
	padding: .2em .4em;
	border-radius: 5px;
}

.fee-dismiss {
	display: block;
	float: right;
	cursor: pointer;
	margin-right: 2px;
	margin-top: -2px;
}

/* Hover */
.fee-hover-border,
.fee-hover-container {
	background-color: #777;
	position: absolute;
	z-index: 999;
}

/* Buttons */
.fee-hover-container button {
	display: block;
	float: left;
	cursor: pointer;
	background-color: transparent;
	border: 0;
	color: #fff;
	font-size: 14px;
	line-height: 16px;
	margin: 0;
	padding: 2px 3px 3px;
}

.fee-hover-container button:hover {
	color: #21759B;
	background-color: #e4e4e4;
}

button.fee-form-save {
	font-weight: bold;
}

button.fee-form-cancel {
}

/* Select */
span.fee-form select {
	vertical-align: -2px;
}

/* Suggest */
.fee-suggest-results {
	padding: 0;
	margin: 0;
	list-style: none;
	position: absolute;
	display: none;
	z-index: 10000;
	border-width: 1px;
	border-style: solid;
}

.fee-suggest-results li {
	padding: 2px 5px;
	white-space: nowrap;
	text-align: left;
}

.fee-suggest-match {
	text-decoration: underline;
}

.fee-suggest-over {
	cursor: pointer;
}

/* Suggest colors */
.fee-suggest-results {
	background-color: #fff;
	border-color: gray;
}

.fee-suggest-results li {
	color: #101010;
}

.fee-suggest-match {
	color: #000;
}

.fee-suggest-over {
	background-color: #f0f0b8;
}

/* Image */
#TB_window #TB_title {
	background-color: #222;
	color: #CFCFCF;
	height: 27px;
}

#fee-img-revert {
	display: block;
	float: left;
	padding: 6px 10px 0;
}

#fee-img-revert:link,
#fee-img-revert:active,
#fee-img-revert:visited {
	color: #21759B !important;
}

#fee-img-revert:hover {
	color: #D54E21 !important;
}

/* Aloha Editor */
button.ImageWP {
	background-image: url('../img/image.gif');
}

