DROP PROCEDURE CalculateAggregateMatch;
DELIMITER ;;
CREATE PROCEDURE CalculateAggregateMatch(IN matchId INT(11) UNSIGNED)
  BEGIN
    DELETE FROM results
    WHERE match_id = matchId;

    SELECT MAX(count)
    INTO @maxCount
    FROM (
           SELECT COUNT(r.id) AS count
           FROM results r
           WHERE r.score_whole > 0
                 AND r.match_id IN (
             SELECT mr1.match_id
             FROM match_ranges mr1
               JOIN match_ranges mr2 ON mr1.range_id = mr2.range_id
             WHERE mr2.match_id = matchId
             GROUP BY mr1.range_id
           )
           GROUP BY r.shooter_id, r.grade_id
         ) t;

    INSERT INTO results (match_id, shooter_id, grade_id, place, shots, score_whole, score_partial)
      SELECT
        matchId,
        shooter_id,
        grade_id,
        0,
        shots,
        score_whole,
        score_partial
      FROM (
             SELECT
               r.shooter_id,
               r.grade_id,
               GROUP_CONCAT(r.shots SEPARATOR '') AS shots,
               SUM(r.score_whole)                 AS score_whole,
               SUM(r.score_partial)               AS score_partial
             FROM results r
             WHERE r.score_whole > 0 AND r.match_id IN (
               SELECT mr1.match_id
               FROM match_ranges mr1
                 JOIN match_ranges mr2 ON mr1.range_id = mr2.range_id
               WHERE mr2.match_id = matchId
               GROUP BY mr1.range_id
             )
             GROUP BY r.shooter_id, r.grade_id
             HAVING COUNT(r.id) >= @maxCount
           ) t;
  END
;;
DELIMITER ;

DELIMITER ;;
CREATE PROCEDURE CalculateDivisionalAggregateMatch(IN matchId INT(11) UNSIGNED)
  BEGIN
    DELETE FROM results
    WHERE match_id = matchId;

    SELECT MAX(count)
    INTO @maxCount
    FROM (
           SELECT COUNT(r.id) AS count
           FROM results r
             JOIN matches m ON m.id = r.match_id
             JOIN match_ranges mr ON mr.match_id = m.id
             JOIN subevents_ranges sr ON sr.range_id = mr.range_id
             JOIN subevents se ON se.id = sr.subevent_id
             JOIN event_shooter_divisions esd ON esd.shooter_id = r.shooter_id AND esd.event_id = m.event_id AND
                                                 (esd.subevent_id = NULL OR esd.subevent_id = se.id)
           WHERE r.score_whole > 0
                 AND r.match_id IN (
             SELECT mr1.match_id
             FROM match_ranges mr1
               JOIN match_ranges mr2 ON mr1.range_id = mr2.range_id
             WHERE mr2.match_id = matchId
             GROUP BY mr1.range_id
           )
           GROUP BY r.shooter_id, esd.division_id
         ) t;


    INSERT INTO results (match_id, shooter_id, grade_id, place, shots, score_whole, score_partial)
      SELECT
        matchId,
        shooter_id,
        grade_id,
        0,
        shots,
        score_whole,
        score_partial
      FROM (
             SELECT
               r.shooter_id,
               r.grade_id,
               GROUP_CONCAT(r.shots SEPARATOR '') AS shots,
               SUM(r.score_whole)                 AS score_whole,
               SUM(r.score_partial)               AS score_partial
             FROM results r
               JOIN matches m ON m.id = r.match_id
               JOIN match_ranges mr ON mr.match_id = m.id
               JOIN subevents_ranges sr ON sr.range_id = mr.range_id
               JOIN subevents se ON se.id = sr.subevent_id
               JOIN event_shooter_divisions esd ON esd.shooter_id = r.shooter_id AND esd.event_id = m.event_id AND
                                                   (esd.subevent_id = NULL OR esd.subevent_id = se.id)
             WHERE r.score_whole > 0
                   AND r.match_id IN (
               SELECT mr1.match_id
               FROM match_ranges mr1
                 JOIN match_ranges mr2 ON mr1.range_id = mr2.range_id
               WHERE mr2.match_id = matchId
               GROUP BY mr1.range_id
             )
             GROUP BY r.shooter_id, esd.division_id
             HAVING COUNT(r.id) >= @maxCount
           ) t;
  END
;;
DELIMITER ;
