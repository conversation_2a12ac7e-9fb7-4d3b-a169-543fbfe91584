CREATE TABLE `event_entry_form_settings` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `event_id` int(10) unsigned NOT NULL,
  `subevent_id` int(10) unsigned DEFAULT NULL,
  `price` decimal(9,2) DEFAULT NULL,
  `entry_form_info` text,
  `data` text,
  PRIMARY KEY (`id`),
  <PERSON><PERSON>Y `event_fk` (`event_id`),
  <PERSON><PERSON>Y `subevent_fk` (`subevent_id`),
  CONSTRAINT `event_entry_form_settings_ibfk_1` FOREIGN KEY (`event_id`) REFERENCES `events` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
  CONSTRAINT `event_entry_form_settings_ibfk_2` FOREIGN KEY (`subevent_id`) REFERENCES `subevents` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=latin1