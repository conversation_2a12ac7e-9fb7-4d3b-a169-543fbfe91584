define({
	"error.0": "Під час перевірки цього URL виникла помилка.",
	"error.400": "Неправильний запит. Запит не може бути виконано через неправильний синтаксис.",
	"error.401": "Не авторизовано. Не пройдена або відсутня аутентифікація.",
	"error.402": "Платіж обов’язковий.",
	"error.403": "Заборонено. Запит правильний, проте сервер не може на нього відповісти.",
	"error.404": "Не знайдено. Ресурс за таким запитом не знайдено, проте можливо буде доступний в майбутьньому.",
	"error.405": "Метод заборонено.",
	"error.406": "Не прийнято. Ваш браузер не підтримує даний тип контету.",
	"error.407": "Проксі-аутентифікація обов’язкова.",
	"error.408": "Затримка запиту. Сервер затримує відповіть очікуючи запит.",
	"error.409": "Конфлікт у запиті.",
	"error.410": "Ресурс був видалений.",
	"error.411": "Необхідна довжина. Це посилання може працювати у браузерах.",
	"error.412": "Умова невірна. Це посилання може працювати у браузерах.",
	"error.413": "Розмір запиту занадто великий. Запит більший ніж сервер опрацьовує чи здатний опрацювати.",
	"error.414": "URI запиту занадто великий. Наданий URI занадто великий для обробки сервером.",
	"error.415": "Тип медіа не підтримується. Інформація, яку запитують, має тип, який не підтримує сервер.",
	"error.416": "Діапазон запиту не може бути досягнуто. Клієнт запитав частину файла, проте сервер не може забезпечити цю частину.",
	"error.417": "Очікуване не відповідно. Север не може задовольнити вимоги вашого браузера.",
	"error.418": "Я - чайник. ;-)",
	"error.500": "Внутрішня помилка сервера. Загальне повідомлення про помилку, надається тоді, коли немає більш відповідного повідомлення.",
	"error.501": "Не реалізовано. Сервер навіть не розпізнав метод запиту чи в нього немає змоги задовольнити запит.",
	"error.502": "Неправильний шлюз. Сервер виступає у ролі шлюзу чи проксі и отримав неправильну відповідь від наступного сервера.",
	"error.503": "Сервіс недоступний. Сервер зараз недоступний (тому що він перезавантажується чи вимкнений з технічних причин). Як правило, це тимчасовий стан.",
	"error.504": "Шлюз не відповідає. Сервер виступає у ролі шлюза чи проксі і не отримав вчасної відповіді від наступного за ним сервера.",
	"error.505": "Версія HTTP не пидтримується. Сервер не підтримує версію HTTP протокола, що викорустовується у запиті."
});
