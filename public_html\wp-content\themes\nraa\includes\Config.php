<?php defined('ABSPATH') or die('No direct script access.');

/**
 * Static helper class for dealing with Configuration files
 *
 * <AUTHOR> Group Pty Ltd
 */
class Config
{
    private static $_configs = array();

    public static function get_value($config_name, $key)
    {
        $config = self::get($config_name);

        if (!$config || !property_exists($config, $key))
            return null;

        return $config->$key;
    }

    public static function get($config_name)
    {
        if (!array_key_exists($config_name, self::$_configs)) {
            // Try to load the config file
            $config = include "config/$config_name.php";

            // Force it to an object
            if (!is_object($config)) {
                if (!is_array($config))
                    return null;

                $config = (object)$config;
            }
            self::$_configs[$config_name] = $config;

            if (!self::$_configs[$config_name])
                return null;
        }

        return self::$_configs[$config_name];
    }
}
