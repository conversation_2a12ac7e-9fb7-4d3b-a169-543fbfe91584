DROP PROCEDURE IF EXISTS `GradeShooters`;
CREATE PROCEDURE `GradeShooters`()
  BEGIN
-- Need these to count number of shoots and calculate grade
    SET @sid := 0, @row := 0, @time := UNIX_TIMESTAMP(NOW()), @start_date := UNIX_TIMESTAMP(
        DATE_SUB(NOW(), INTERVAL 2 YEAR));

-- I suppose its easier to clear and replace all grades each time than to prune ones that have fallen out of validity
    TRUNCATE TABLE shooter_calculated_grades;

-- Recalculate every shooters grade
    INSERT INTO shooter_calculated_grades (shooter_id, avg_score, grade_id)
      SELECT
        s.id,
        AVG(Graded.Score / HighScore.Score * 100) AS avg_score,
        GetGrade(AVG(Graded.Score / HighScore.Score * 100), Graded.discipline_id)
      FROM shooters s
        JOIN (
               SELECT
                 r.shooter_id,
                 r.discipline_id,
                 r.match_id,
                 CombineScore(r.score_whole, r.score_partial) AS Score,
                 @row := IF(@sid = r.shooter_id, @row + 1, 1) AS row_number,
                 @sid := r.shooter_id                         AS unused
               FROM (
                      SELECT
                        r.shooter_id,
                        r.grade_id,
                        r.match_id,
                        r.score_whole,
                        r.score_partial,
                        g.discipline_id
                      FROM results r
                        JOIN matches m ON m.id = r.match_id
                        JOIN events e ON e.id = m.event_id
                        JOIN grades g ON g.id = r.grade_id
                      WHERE m.is_graded = 1
                            AND e.is_team_event = 0
                            AND e.end_date >= @start_date
                      ORDER BY r.shooter_id, e.end_date DESC, m.number DESC
                    ) r
               GROUP BY r.shooter_id, r.match_id, r.discipline_id
             ) Graded ON Graded.shooter_id = s.id
        JOIN (
               SELECT
                 g.discipline_id,
                 r.match_id,
                 MAX(CombineScore(r.score_whole, r.score_partial)) AS Score
               FROM results r
                 JOIN grades g ON g.id = r.grade_id
                 JOIN matches m ON m.id = r.match_id
                 JOIN events e ON e.id = m.event_id
               WHERE m.is_graded = 1
                     AND e.is_team_event = 0
                     AND e.end_date >= @start_date
               GROUP BY m.id, g.discipline_id
             ) HighScore ON HighScore.discipline_id = Graded.discipline_id AND HighScore.match_id = Graded.match_id
      WHERE Graded.row_number <= 8
      GROUP BY s.id, Graded.discipline_id;

-- Update these to add in number of shoots: Note: I am concerned with my lack of discipline grouping
    UPDATE shooter_calculated_grades scg
      JOIN grades g ON scg.grade_id = g.id
      JOIN (
             SELECT
               r.shooter_id,
               g.discipline_id,
               IF(COUNT(*) < 8, COUNT(*), 8) AS shoots
             FROM results r
               JOIN grades g ON r.grade_id = g.id
               JOIN matches m      ON r.match_id = m.id
               JOIN events e       ON m.event_id = e.id
             WHERE m.is_graded = 1
                   AND e.is_team_event = 0
                   AND e.end_date >= @start_date
             GROUP BY r.shooter_id, g.discipline_id
           ) Shoots ON Shoots.shooter_id = scg.shooter_id AND Shoots.discipline_id = g.discipline_id
    SET number_of_shoots = Shoots.shoots;

-- Update audit trail timestamps for grades that have not changed
    UPDATE audit_trails a
      JOIN (
             SELECT
               a.id,
               MAX(a.timestamp)
             FROM shooter_calculated_grades scg
               JOIN grades g ON g.id = scg.grade_id
               JOIN audit_trails a ON a.row_id = scg.id AND a.table_name = 'shooter_calculated_grades'
               JOIN audit_trail_changes ac ON ac.audit_trail_id = a.id
             WHERE (ac.column_name = 'shooter_id' AND ac.to = scg.shooter_id) OR
                   (ac.column_name = 'grade_id' AND ac.to = scg.grade_id) OR
                   (ac.column_name = 'avg_score' AND ac.to = scg.avg_score) OR
                   (ac.column_name = 'number_of_shoots' AND ac.to = scg.number_of_shoots)
             GROUP BY scg.shooter_id, g.discipline_id
           ) a2 ON a.id = a2.id
    SET a.`timestamp` = @time;

-- Find the last audit trail for this shooter to determine if there has been any change
    INSERT INTO audit_trails (`user_id`, `table_name`, `row_id`, `type`, `timestamp`)
      SELECT
        1,
        'shooter_calculated_grades',
        scg.id,
        IF(MAX(a.timestamp) IS NULL, 2, 3),
        @time
      FROM shooter_calculated_grades scg
        JOIN grades g ON g.id = scg.grade_id
        LEFT OUTER JOIN audit_trails a ON a.row_id = scg.id
        LEFT OUTER JOIN audit_trail_changes ac ON ac.audit_trail_id = a.id
      WHERE a.id IS NULL
            OR (a.table_name = 'shooter_calculated_grades' AND
                ac.column_name = 'avg_score' AND
                ac.to <> scg.avg_score)
      GROUP BY scg.shooter_id, g.discipline_id
      ORDER BY a.timestamp DESC;

-- Add audit trail changes for updates
    INSERT INTO audit_trail_changes (`audit_trail_id`, `column_name`, `from`, `to`)
      SELECT
        a2.id,
        ac.column_name,
        ac.to,
        CASE ac.column_name
        WHEN 'grade_id' THEN scg.grade_id
        WHEN 'avg_score' THEN scg.avg_score
        WHEN 'number_of_shoots' THEN scg.number_of_shoots END
      FROM shooter_calculated_grades scg
        JOIN audit_trails a ON a.row_id = scg.id
        JOIN audit_trail_changes ac ON ac.audit_trail_id = a.id
        JOIN audit_trails a2
          ON a2.timestamp = @time AND a2.user_id = 1 AND a2.table_name = 'shooter_calculated_grades' AND a2.type = 3
      WHERE a.table_name = 'shooter_calculated_grades'
            AND (
        (ac.column_name = 'grade_id' AND ac.to <> scg.grade_id) OR
        (ac.column_name = 'avg_score' AND ac.to <> scg.avg_score) OR
        (ac.column_name = 'number_of_shoots' AND ac.to <> scg.number_of_shoots)
      )
      GROUP BY ac.column_name
      ORDER BY a.timestamp DESC;

-- Add audit trail changes for new inserts
    INSERT INTO `audit_trail_changes` (`audit_trail_id`, `column_name`, `from`, `to`)
      SELECT
        a.id,
        'shooter_id',
        NULL,
        scg.shooter_id
      FROM audit_trails a
        JOIN shooter_calculated_grades scg ON scg.id = a.row_id
      WHERE a.row_id = scg.id
            AND a.table_name = 'shooter_calculated_grades'
            AND a.type = 2
            AND a.timestamp = @time
            AND a.user_id = 1
            AND NOT EXISTS(
          SELECT
            *
          FROM audit_trail_changes ac
          WHERE ac.audit_trail_id = a.id
                AND ac.column_name = 'shooter_id'
      );

    INSERT INTO `audit_trail_changes` (`audit_trail_id`, `column_name`, `from`, `to`)
      SELECT
        a.id,
        'grade_id',
        NULL,
        scg.grade_id
      FROM audit_trails a
        JOIN shooter_calculated_grades scg ON scg.id = a.row_id
      WHERE a.row_id = scg.id
            AND a.table_name = 'shooter_calculated_grades'
            AND a.type = 2
            AND a.timestamp = @time
            AND a.user_id = 1
            AND NOT EXISTS(
          SELECT
            *
          FROM audit_trail_changes ac
          WHERE ac.audit_trail_id = a.id
                AND ac.column_name = 'grade_id'
      );

    INSERT INTO `audit_trail_changes` (`audit_trail_id`, `column_name`, `from`, `to`)
      SELECT
        a.id,
        'avg_score',
        NULL,
        scg.avg_score
      FROM audit_trails a
        JOIN shooter_calculated_grades scg ON scg.id = a.row_id
      WHERE a.row_id = scg.id
            AND a.table_name = 'shooter_calculated_grades'
            AND a.type = 2
            AND a.timestamp = @time
            AND a.user_id = 1
            AND NOT EXISTS(
          SELECT
            *
          FROM audit_trail_changes ac
          WHERE ac.audit_trail_id = a.id
                AND ac.column_name = 'avg_score'
      );

    INSERT INTO `audit_trail_changes` (`audit_trail_id`, `column_name`, `from`, `to`)
      SELECT
        a.id,
        'number_of_shoots',
        NULL,
        scg.number_of_shoots
      FROM audit_trails a
        JOIN shooter_calculated_grades scg ON scg.id = a.row_id
      WHERE a.row_id = scg.id
            AND a.table_name = 'shooter_calculated_grades'
            AND a.type = 2
            AND a.timestamp = @time
            AND a.user_id = 1
            AND NOT EXISTS(
          SELECT
            *
          FROM audit_trail_changes ac
          WHERE ac.audit_trail_id = a.id
                AND ac.column_name = 'number_of_shoots'
      );
  END;
