define({
	"size.natural": "Tamanho original",
	"button.addimg.tooltip": "Adicionar referência a imagem",
	"floatingmenu.tab.img": "Imagem",
	"floatingmenu.tab.formatting": "Formatando",
	"floatingmenu.tab.resize": "Redimensionar",
	"floatingmenu.tab.crop": "Cortar",
	"button.uploadimg.tooltip": "Upar imagem",
	"button.uploadimg.label": "Upload",
	"button.img.align.left.tooltip": "Alinhar a esquerda",
	"button.img.align.right.tooltip": "Alinhar a direita",
	"button.img.align.none.tooltip": "Nenhum alinhamento",
	"field.img.title.label": "Título",
	"field.img.title.tooltip": "Título",
	"field.img.label": "URL",
	"field.img.tooltip": "Fonte",
	"border ": "Adicionar borda a imagem",
	"padding.increase ": "Aumentar espaçamento",
	"padding.decrease ": "Diminuir espaçamento",
	"size.increase ": "Aumentar o tamanho",
	"size.decrease ": "Diminuir o tamanho",
	"Resize": "Redimensionar",
	"Crop": "Cortar",
	"Reset": "Resetar",
	"Accept": "Aceitar",
	"Cancel": "Cancelar",
	"height": "Largura",
	"width": "Altura",
	"button.toggle.tooltip": "Alternar manter a relação de aspecto",
	"field.img.src.label": "Fonte",
	"field.img.src.tooltip": "Fonte"
});
