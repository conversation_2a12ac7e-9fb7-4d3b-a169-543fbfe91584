# Translation of Plugins - TinyMCE Advanced - Stable (latest release) in English (Australia)
# This file is distributed under the same license as the Plugins - TinyMCE Advanced - Stable (latest release) package.
msgid ""
msgstr ""
"PO-Revision-Date: 2019-11-13 07:02:34+0000\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=n != 1;\n"
"X-Generator: GlotPress/2.4.0-alpha\n"
"Language: en_AU\n"
"Project-Id-Version: Plugins - TinyMCE Advanced - Stable (latest release)\n"

#. Description of the plugin
msgid "Extends and enhances the block editor (<PERSON><PERSON><PERSON>) and the classic editor (TinyMCE)."
msgstr "Extends and enhances the block editor (<PERSON><PERSON><PERSON>) and the classic editor (TinyMCE)."

#: tadv_admin.php:845
msgid "Keep paragraph tags in the Classic block and the Classic Editor"
msgstr "Keep paragraph tags in the Classic block and the Classic Editor"

#: tinymce-advanced.php:881
msgid "Clear formatting"
msgstr "Clear formatting"

#: tadv_admin.php:963
msgid "It is important that the exported file is not edited in any way."
msgstr "It is important that the exported file is not edited in any way."

#. translators: URL to (localised) Advanced TinyMCE Configuration plugin.
#: tadv_admin.php:949
msgid "https://wordpress.org/plugins/advanced-tinymce-configuration/"
msgstr "https://en-au.wordpress.org/plugins/advanced-tinymce-configuration/"

#: tadv_admin.php:937
msgid "To keep the table more responsive please use percentage values when setting widths."
msgstr "To keep the table more responsive please use percentage values when setting widths."

#: tadv_admin.php:933
msgid "The advanced tabs allow setting of inline CSS styles on the table, each row, and each cell. They have fields for easier setting of border, border color and background color styles."
msgstr "The advanced tabs allow setting of inline CSS styles on the table, each row, and each cell. They have fields for easier setting of border, border colour and background colour styles."

#: tadv_admin.php:931
msgid "Show the advanced tabs in the table properties dialogs"
msgstr "Show the advanced tabs in the table properties dialogues"

#: tadv_admin.php:926
msgid "When disabled, pressing the tab key will jump outside the editor area."
msgstr "When disabled, pressing the tab key will jump outside the editor area."

#: tadv_admin.php:924
msgid "Jump to the next cell when pressing the tab key while editing a table"
msgstr "Jump to the next cell when pressing the tab key while editing a table"

#: tadv_admin.php:919
msgid "If the grid is disabled the number of rows and columns can be typed in the Insert Table dialog."
msgstr "If the grid is disabled the number of rows and columns can be typed in the Insert Table dialogue."

#: tadv_admin.php:917
msgid "When inserting a table show a grid where the number of rows and columns can be selected by dragging with the mouse"
msgstr "When inserting a table show a grid where the number of rows and columns can be selected by dragging with the mouse"

#: tadv_admin.php:912
msgid "To set other default attributes or inline styles use the Advanced TinyMCE Configuration plugin."
msgstr "To set other default attributes or inline styles use the Advanced TinyMCE Configuration plugin."

#: tadv_admin.php:911
msgid "This will add a border around the table unless it is overriden by your theme."
msgstr "This will add a border around the table unless it is overridden by your theme."

#: tadv_admin.php:909
msgid "When inserting a table set the HTML border attribute to 1"
msgstr "When inserting a table set the HTML border attribute to 1"

#: tadv_admin.php:904
msgid "This option does not affect inline styles on tables in existing posts. To reset table size or remove all formatting for the whole table please see the two buttons at the bottom of the Format menu."
msgstr "This option does not affect inline styles on tables in existing posts. To reset table size or remove all formatting for the whole table please see the two buttons at the bottom of the Format menu."

#: tadv_admin.php:900
msgid "Then the table, the rows and the cells can be resized by typing the size values in the advanced options tabs."
msgstr "Then the table, the rows and the cells can be resized by typing the size values in the advanced options tabs."

#: tadv_admin.php:899
msgid "Disabling this option will stop the editor from adding inline CSS styles and will produce cleaner HTML code."
msgstr "Disabling this option will stop the editor from adding inline CSS styles and will produce cleaner HTML code."

#: tadv_admin.php:896
msgid "When a row or a column is resized the inline styles are updated on all table rows and cells."
msgstr "When a row or a column is resized the inline styles are updated on all table rows and cells."

#: tadv_admin.php:895
msgid "This may override some styles that are set by your theme and usually makes the table non-responsive when viewed on a small screen like a smartphone."
msgstr "This may override some styles that are set by your theme and usually makes the table non-responsive when viewed on a small screen like a smartphone."

#: tadv_admin.php:894
msgid "When enabled the whole table, rows, and columns can be resized by dragging but the sizes are set with inline CSS styles."
msgstr "When enabled the whole table, rows, and columns can be resized by dragging but the sizes are set with inline CSS styles."

#: tadv_admin.php:892
msgid "Enable resizing of tables, rows, and columns by dragging with the mouse"
msgstr "Enable resizing of tables, rows, and columns by dragging with the mouse"

#: tadv_admin.php:889
msgid "Advanced options for tables"
msgstr "Advanced options for tables"

#: tadv_admin.php:847
msgid "Stop removing &lt;p&gt; and &lt;br&gt; tags in the Classic Editor and show them in the Text tab."
msgstr "Stop removing &lt;p&gt; and &lt;br&gt; tags in the Classic Editor and show them in the Text tab."

#. translators: URL to (localised) Classic Editor plugin.
#: tadv_admin.php:833
msgid "https://wordpress.org/plugins/classic-editor/"
msgstr "https://en-au.wordpress.org/plugins/classic-editor/"

#: tadv_admin.php:812
msgid "Selecting this option also adds some improvements and fixes for the Classic block."
msgstr "Selecting this option also adds some improvements and fixes for the Classic block."

#: tadv_admin.php:811
msgid "The default block is inserted on pressing Enter in the title, or clicking under the last block."
msgstr "The default block is inserted on pressing Enter in the title, or clicking under the last block."

#: tadv_admin.php:809
msgid "Make the Classic Paragraph or Classic block the default block (hybrid mode)"
msgstr "Make the Classic Paragraph or Classic block the default block (hybrid mode)"

#: tadv_admin.php:794
msgid "Add Classic Paragraph block"
msgstr "Add Classic Paragraph block"

#: tadv_admin.php:584
msgid "Toolbars for the Classic Editor"
msgstr "Toolbars for the Classic Editor"

#: tadv_admin.php:538
msgid "Unused Buttons for the Classic Paragraph and Classic blocks toolbars"
msgstr "Unused Buttons for the Classic Paragraph and Classic blocks toolbars"

#: tadv_admin.php:179
msgid "Also currently the drop-down should not be empty and the Inline Image item cannot be placed in the side toolbar."
msgstr "Also currently the drop-down should not be empty and the Inline Image item cannot be placed in the side toolbar."

#: tadv_admin.php:176
msgid "All buttons that are shown in the drop-down are auto-arranged by alphabetical order. The users are not allowed to arrange them."
msgstr "All buttons that are shown in the drop-down are auto-arranged by alphabetical order. The users are not allowed to arrange them."

#: tadv_admin.php:175
msgid "All other buttons are always shown in a drop-down. The users are not allowed to add any of them to the main toolbar."
msgstr "All other buttons are always shown in a drop-down. The users are not allowed to add any of them to the main toolbar."

#: tadv_admin.php:174
msgid "The Align Left, Align Center, Align Right, Bold, Italic, and Link buttons cannot be moved or arranged."
msgstr "The Align Left, Align Centre, Align Right, Bold, Italic, and Link buttons cannot be moved or arranged."

#: tadv_admin.php:167
msgid "Limitations for the Block Editor toolbar"
msgstr "Limitations for the Block Editor toolbar"

#: tadv_admin.php:94
msgid "Importing of settings failed. The imported file is invalid."
msgstr "Importing of settings failed. The imported file is invalid."

#: tadv_admin.php:91
msgid "Importing of settings failed. The imported file is empty."
msgstr "Importing of settings failed. The imported file is empty."

#: tadv_admin.php:88
msgid "Importing of settings failed. Please import a valid settings file."
msgstr "Importing of settings failed. Please import a valid settings file."

#: tadv_admin.php:81 tadv_admin.php:99
msgid "Settings imported successfully."
msgstr "Settings imported successfully."

#: tadv_admin.php:60
msgid "Import settings from string"
msgstr "Import settings from string"

#: tadv_admin.php:51
msgid "Import settings"
msgstr "Import settings"

#: tadv_admin.php:49
msgid "The settings are imported from a previously exported settings file."
msgstr "The settings are imported from a previously exported settings file."

#: tinymce-advanced.php:889
msgid "Text Background Color"
msgstr "Text Background Colour"

#: tinymce-advanced.php:887
msgid "Text Color"
msgstr "Text Colour"

#: tinymce-advanced.php:885
msgid "Underline"
msgstr "Underline"

#: tinymce-advanced.php:884
msgid "Mark"
msgstr "Mark"

#: tinymce-advanced.php:883
msgid "Subscript"
msgstr "Subscript"

#: tinymce-advanced.php:882
msgid "Superscript"
msgstr "Superscript"

#: tinymce-advanced.php:851
msgid "For use instead of the Paragraph Block. Supports transforming to and from multiple Paragraph blocks, Image, Table, List, Quote, Custom HTML, and most other blocks."
msgstr "For use instead of the Paragraph Block. Supports transforming to and from multiple Paragraph blocks, Image, Table, List, Quote, Custom HTML, and most other blocks."

#: tadv_admin.php:983
msgid "TinyMCE editors on the front end of the site"
msgstr "TinyMCE editors on the front end of the site"

#: tadv_admin.php:979
msgid "Other TinyMCE editors in wp-admin"
msgstr "Other TinyMCE editors in wp-admin"

#: tadv_admin.php:972
msgid "Enable the TinyMCE editor enhancements for:"
msgstr "Enable the TinyMCE editor enhancements for:"

#: tadv_admin.php:802
msgid "In addition most default blocks can be transformed into classic paragraphs, and a Classic Paragraph can be converted to multiple blocks."
msgstr "In addition most default blocks can be transformed into classic paragraphs, and a Classic Paragraph can be converted to multiple blocks."

#: tadv_admin.php:756
msgid "Append all buttons to the top toolbar in the Classic Paragraph and Classic blocks."
msgstr "Append all buttons to the top toolbar in the Classic Paragraph and Classic blocks."

#: tadv_admin.php:418
msgid "The toolbars in the Classic Paragraph and Classic blocks are narrower and show on focus."
msgstr "The toolbars in the Classic Paragraph and Classic blocks are narrower and show on focus."

#: tadv_admin.php:415
msgid "Toolbars for the Classic Paragraph and Classic blocks"
msgstr "Toolbars for the Classic Paragraph and Classic blocks"

#: tadv_admin.php:396
msgid "Enable setting of selected text background color"
msgstr "Enable setting of selected text background colour"

#: tadv_admin.php:391 tadv_admin.php:404
msgid "No"
msgstr "No"

#: tadv_admin.php:387 tadv_admin.php:400
msgid "Yes"
msgstr "Yes"

#: tadv_admin.php:383
msgid "Enable setting of selected text color"
msgstr "Enable setting of selected text colour"

#: tinymce-advanced.php:890 tadv_admin.php:376
msgid "Selected text background color"
msgstr "Selected text background colour"

#: tinymce-advanced.php:888 tadv_admin.php:372
msgid "Selected text color"
msgstr "Selected text colour"

#: tadv_admin.php:368
msgid "Text color"
msgstr "Text colour"

#: tadv_admin.php:343
msgid "Unused buttons for the blocks toolbars"
msgstr "Unused buttons for the blocks toolbars"

#: tinymce-advanced.php:880 tadv_admin.php:314
msgid "Formatting"
msgstr "Formatting"

#: tadv_admin.php:310
msgid "(shown in the sidebar)"
msgstr "(shown in the sidebar)"

#: tadv_admin.php:309
msgid "Alternative side toolbar"
msgstr "Alternative side toolbar"

#: tadv_admin.php:163
msgid "Main toolbar"
msgstr "Main toolbar"

#: tadv_admin.php:153
msgid "Toolbars for the Block Editor"
msgstr "Toolbars for the Block Editor"

#: tadv_admin.php:148
msgid "Classic Editor (TinyMCE)"
msgstr "Classic Editor (TinyMCE)"

#: tadv_admin.php:143
msgid "Block Editor (Gutenberg)"
msgstr "Block Editor (Gutenberg)"

#: tadv_admin.php:803
msgid "It can be used everywhere instead of the Paragraph block including in columns, when creating reusable blocks, etc."
msgstr "It can be used everywhere instead of the Paragraph block including in columns, when creating reusable blocks, etc."

#: tadv_admin.php:798
msgid "Also, like the Classic block, most existing TinyMCE plugins and add-ons will continue to work."
msgstr "Also, like the Classic block, most existing TinyMCE plugins and add-ons will continue to work."

#: tadv_admin.php:797
msgid "You can add multiple paragraphs, tables, galleries, embed video, set fonts and colors, and generally use everything that is available in the Classic Editor."
msgstr "You can add multiple paragraphs, tables, galleries, embed video, set fonts and colours, and generally use everything that is available in the Classic Editor."

#: tadv_admin.php:796
msgid "The Classic Paragraph block includes the familiar TinyMCE editor and is an extended and enhanced Classic block."
msgstr "The Classic Paragraph block includes the familiar TinyMCE editor and is an extended and enhanced Classic block."

#: tinymce-advanced.php:849
msgid "Classic Paragraph"
msgstr "Classic Paragraph"

#: tadv_admin.php:947
msgid "For other advanced TinyMCE settings, including settings for the Classic Paragraph block and more advanced table options, you can use the %1$sAdvanced TinyMCE Configuration plugin%2$s."
msgstr "For other advanced TinyMCE settings, including settings for the Classic Paragraph block and more advanced table options, you can use the %1$sAdvanced TinyMCE Configuration plugin%2$s."

#: tadv_admin.php:879
msgid "Disabled:"
msgstr "Disabled:"

#: tadv_admin.php:831
msgid "If you prefer to use both editors side by side, do not enable this option. It is better to install the %1$sClassic Editor plugin%2$s."
msgstr "If you prefer to use both editors side by side, do not enable this option. It is better to install the %1$sClassic Editor plugin%2$s."

#: tadv_admin.php:826
msgid "It will allow you to use other plugins that enhance that editor, add old-style Meta Boxes, or in some way depend on the previous Edit Post screen."
msgstr "It will allow you to use other plugins that enhance that editor, add old-style Meta Boxes, or in some way depend on the previous Edit Post screen."

#: tadv_admin.php:825
msgid "Selecting this option will restore the previous (&#8220;classic&#8221;) editor and the previous Edit Post screen."
msgstr "Selecting this option will restore the previous (&#8220;classic&#8221;) editor and the previous Edit Post screen."

#: tadv_admin.php:823
msgid "Replace the Block Editor with the Classic Editor"
msgstr "Replace the Block Editor with the Classic Editor"

#: tadv_admin.php:799
msgid "This makes the Block Editor more familiar, easier to use, easier to get used to, and more compatible with your existing workflow."
msgstr "This makes the Block Editor more familiar, easier to use, easier to get used to, and more compatible with your existing workflow."

#: tadv_admin.php:757
msgid "This affects buttons that are added by other plugins. These buttons will be appended to the top toolbar row instead of forming second, third, and forth rows."
msgstr "This affects buttons that are added by other plugins. These buttons will be appended to the top toolbar row instead of forming second, third, and fourth rows."

#: tadv_admin.php:425
msgid "Enable the editor menu (recommended)."
msgstr "Enable the editor menu (recommended)."

#: tadv_admin.php:420
msgid "The buttons will wrap around depending on the width of the toolbar."
msgstr "The buttons will wrap around depending on the width of the toolbar."

#: tadv_admin.php:419
msgid "For best results enable the menu and add only essential buttons."
msgstr "For best results enable the menu and add only essential buttons."

#: tadv_admin.php:535 tadv_admin.php:703
msgid "Drop buttons in the toolbars, or drag the buttons to rearrange them."
msgstr "Drop buttons in the toolbars, or drag the buttons to rearrange them."

#: tadv_admin.php:783
msgid "Replace the size setting available for fonts with: %s."
msgstr "Replace the size setting available for fonts with: %s."

#: tadv_admin.php:880
msgid "A stylesheet file named editor-style.css was not added by your theme."
msgstr "A style sheet file named editor-style.css was not added by your theme."

#: tinymce-advanced.php:1283
msgid "Settings"
msgstr "Settings"

#: tadv_admin.php:975
msgid "The Classic Editor (Add New and Edit posts and pages)"
msgstr "The Classic Editor (Add New and Edit posts and pages)"

#: tadv_admin.php:960
msgid "Settings import and export"
msgstr "Settings import and export"

#: tadv_admin.php:870
msgid "Create CSS classes menu"
msgstr "Create CSS classes menu"

#: tadv_admin.php:782
msgid "Font sizes"
msgstr "Font Sizes"

#: tadv_admin.php:777
msgid "Open the TinyMCE link dialog when using the link button on the toolbar or the link menu item."
msgstr "Open the TinyMCE link dialogue when using the link button on the toolbar or the link menu item."

#: tadv_admin.php:776
msgid "Alternative link dialog"
msgstr "Alternative link dialogue"

#: tadv_admin.php:771
msgid "Replace the browser context (right-click) menu."
msgstr "Replace the browser context (right-click) menu."

#: tadv_admin.php:764
msgid "Enable more list options: upper or lower case letters for ordered lists, disk or square for unordered lists, etc."
msgstr "Enable more list options: upper or lower case letters for ordered lists, disk or square for unordered lists, etc."

#: tadv_admin.php:752
msgid "Options"
msgstr "Options"

#. Author URI of the plugin
msgid "http://www.laptoptips.ca/"
msgstr "http://www.laptoptips.ca/"

#. Author of the plugin
msgid "Andrew Ozz"
msgstr "Andrew Ozz"

#. Plugin URI of the plugin
msgid "http://www.laptoptips.ca/projects/tinymce-advanced/"
msgstr "http://www.laptoptips.ca/projects/tinymce-advanced/"

#. Plugin Name of the plugin
msgid "TinyMCE Advanced"
msgstr "TinyMCE Advanced"

#: tinymce-advanced.php:377
msgid "Please upgrade your WordPress installation or download an <a href=\"%s\">older version of the plugin</a>."
msgstr "Please upgrade your WordPress installation or download an <a href=\"%s\">older version of the plugin</a>."

#: tinymce-advanced.php:369
msgid "TinyMCE Advanced requires WordPress version %1$s or newer. It appears that you are running %2$s. This can make the editor unstable."
msgstr "TinyMCE Advanced requires WordPress version %1$s or newer. It appears that you are running %2$s. This can make the editor unstable."

#: tadv_admin.php:1002
msgid "The [Toolbar toggle] button shows or hides the second, third, and forth button rows. It will only work when it is in the first row and there are buttons in the second row."
msgstr "The [Toolbar toggle] button shows or hides the second, third, and fourth button rows. It will only work when it is in the first row and there are buttons in the second row."

#: tadv_admin.php:996
msgid "Restore Default Settings"
msgstr "Restore Default Settings"

#: tadv_admin.php:968
msgid "Import Settings"
msgstr "Import Settings"

#: tadv_admin.php:967
msgid "Export Settings"
msgstr "Export Settings"

#: tadv_admin.php:958
msgid "Administration"
msgstr "Administration"

#: tadv_admin.php:850
msgid "Line breaks in the Text tab in the Classic Editor would still affect the output, in particular do not use empty lines, line breaks inside HTML tags or multiple &lt;br&gt; tags."
msgstr "Line breaks in the Text tab in the Classic Editor would still affect the output, in particular do not use empty lines, line breaks inside HTML tags or multiple &lt;br&gt; tags."

#: tadv_admin.php:849
msgid "However it may behave unexpectedly in rare cases, so test it thoroughly before enabling it permanently."
msgstr "However it may behave unexpectedly in rare cases, so test it thoroughly before enabling it permanently."

#: tadv_admin.php:848
msgid "This will make it possible to use more advanced coding in the Text tab without the back-end filtering affecting it much."
msgstr "This will make it possible to use more advanced coding in the Text tab without the back end filtering affecting it much."

#: tadv_admin.php:872
msgid "Load the CSS classes used in editor-style.css and replace the Formats menu."
msgstr "Load the CSS classes used in editor-style.css and replace the Formats menu."

#: tadv_admin.php:791
msgid "Advanced Options"
msgstr "Advanced Options"

#: tadv_admin.php:770
msgid "Context Menu"
msgstr "Context Menu"

#: tadv_admin.php:762
msgid "List Style Options"
msgstr "List Style Options"

#: tadv_admin.php:706
msgid "Unused Buttons"
msgstr "Unused Buttons"

#: tadv_admin.php:589
msgid "Enable the editor menu."
msgstr "Enable the editor menu."

#: tadv_admin.php:138 tadv_admin.php:997
msgid "Save Changes"
msgstr "Save Changes"

#: tadv_admin.php:126
msgid "Settings saved."
msgstr "Settings saved."

#: tadv_admin.php:119
msgid "Editor Settings"
msgstr "Editor Settings"

#: tadv_admin.php:108
msgid "ERROR: All toolbars are empty. Default settings loaded."
msgstr "ERROR: All toolbars are empty. Default settings loaded."

#: tadv_admin.php:78
msgid "Importing of settings failed."
msgstr "Importing of settings failed."

#: tadv_admin.php:59
msgid "Verify"
msgstr "Verify"

#: tadv_admin.php:56
msgid "Alternatively the settings can be imported from a JSON encoded string. Please paste the exported string in the text area below."
msgstr "Alternatively the settings can be imported from a JSON encoded string. Please paste the exported string in the text area below."

#: tadv_admin.php:45
msgid "TinyMCE Advanced Settings Import"
msgstr "TinyMCE Advanced Settings Import"

#: tadv_admin.php:65
msgid "Back to Editor Settings"
msgstr "Back to Editor Settings"

#: tadv_admin.php:962
msgid "The settings are exported as a JSON encoded file."
msgstr "The settings are exported as a JSON encoded file."

#: tadv_admin.php:38
msgid "Default settings restored."
msgstr "Default settings restored."