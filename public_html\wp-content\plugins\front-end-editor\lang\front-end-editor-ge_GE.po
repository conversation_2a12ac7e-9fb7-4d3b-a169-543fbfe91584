msgid ""
msgstr ""
"Project-Id-Version: Front-end Editor Georgian\n"
"Report-Msgid-Bugs-To: http://wordpress.org/tag/front-end-editor\n"
"POT-Creation-Date: 2010-01-01 19:11+0200\n"
"PO-Revision-Date: \n"
"Last-Translator: <PERSON><PERSON> <<EMAIL>>\n"
"Language-Team: Georgianisation.site88.net <<EMAIL>>\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"
"X-Poedit-Language: Georgian\n"
"X-Poedit-Country: GEORGIA\n"

#. #-#-#-#-#  front-end-editor.pot (Front-end Editor 1.6)  #-#-#-#-#
#. Plugin Name of an extension
#: admin.php:7
msgid "Front-end Editor"
msgstr "Front-end Editor"

#: admin.php:10
msgid "Fields"
msgstr "ველები"

#: admin.php:11
#: scb/AdminPage.php:314
msgid "Settings"
msgstr "პარამეტრები"

#: admin.php:51
#: admin.php:104
#: scb/AdminPage.php:101
msgid "Settings <strong>saved</strong>."
msgstr "პარამეტრები <strong>დამახსოვრებულია</strong>."

#: admin.php:63
msgid "Enable or disable editable fields"
msgstr "რედაქტირებადი ველების ჩართვა გამორთვა"

#: admin.php:65
msgid "Post fields"
msgstr "პოსტის ველები"

#: admin.php:66
msgid "Other fields"
msgstr "სხვა ველები"

#: admin.php:110
msgid "Rich text editor"
msgstr "ტექსტური რედაქტორი"

#: admin.php:111
msgid "Enable the WYSIWYG editor"
msgstr "WYSIWYG რედაქტორის ჩართვა"

#: admin.php:117
msgid "Edit paragraphs"
msgstr "პარაგრაფების რედაქტირება"

#: admin.php:118
msgid "Edit one paragraph at a time, instead of an entire post"
msgstr "ერთ ჯერზე შეცვალე მხოლოდ ერთი პარაგრაფი, მთელი პოსტის ნაცვლად."

#: admin.php:124
msgid "Highlighting"
msgstr "გამოყოფა"

#: admin.php:125
msgid "Highlight editable elements"
msgstr "გამოყავი რედაქტირებადი ველები"

#: core.php:58
msgid "Save"
msgstr "დამახსოვრება"

#: core.php:59
msgid "Cancel"
msgstr "გაუქმება"

#: core.php:90
msgid "Change Image"
msgstr "სურათის შეცვლა"

#: core.php:91
msgid "Use default"
msgstr "სტანდარტულის გამოყენება"

#: fields/base.php:79
msgid "empty"
msgstr "ცარიელი"

#: front-end-editor.php:68
msgid "Post title"
msgstr "პოსტის სათაური"

#: front-end-editor.php:74
msgid "Post content"
msgstr "პოსტის შენაარსი"

#: front-end-editor.php:80
msgid "Post excerpt"
msgstr "პოსტის ამონაწერი"

#: front-end-editor.php:86
msgid "Post category"
msgstr "პოსტის კატეგორია"

#: front-end-editor.php:93
msgid "Post tags"
msgstr "პოსტის ტეგები"

#: front-end-editor.php:100
msgid "Post terms"
msgstr "პოსტის term-ები."

#: front-end-editor.php:107
msgid "Post custom fields"
msgstr "პოსტის დამატებითი ველები"

#: front-end-editor.php:113
msgid "Comment text"
msgstr "კომენტარის ტექსტი"

#: front-end-editor.php:119
msgid "Category title"
msgstr "კატეგორიის სათაური"

#: front-end-editor.php:124
msgid "Tag title"
msgstr "ტეგის სათაური"

#: front-end-editor.php:129
msgid "Author description"
msgstr "ავტორის აღწერა"

#: front-end-editor.php:136
msgid "Widget title"
msgstr "ვიდგეტის სათაური"

#: front-end-editor.php:141
msgid "Text widget content"
msgstr "ტექსტური ვიდგეტის შინაარსი"

#: front-end-editor.php:147
msgid "Site title and description"
msgstr "საიტის სათაური და აღწერა"

#: front-end-editor.php:153
msgid "Theme images"
msgstr "დიზაინის სურათები"

#: scb/AdminPage.php:111
msgid "Save Changes"
msgstr "ცვლილებების დამახსოვრება"

#. Plugin URI of an extension
msgid "http://scribu.net/wordpress/front-end-editor"
msgstr "http://scribu.net/wordpress/front-end-editor"

#. Description of an extension
msgid "Allows you to edit your posts without going through the admin interface"
msgstr "საშუალებას გაძლევთ განახორციელოთ ცვლილებები ადმინ პანელში შეუსვლელად"

#. Author of an extension
msgid "scribu"
msgstr "scribu"

#. Author URI of an extension
msgid "http://scribu.net/"
msgstr "http://scribu.net/"

#~ msgid "Field name"
#~ msgstr "ველის სახელი"

