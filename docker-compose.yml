services:
  database:
    image: mysql:8.0
    init: true
    environment:
      - MYSQL_ROOT_PASSWORD=password
      - MYSQL_DATABASE=nraa_wp
      - MYSQL_ROOT_HOST=%
    volumes:
      - mysql-data:/var/lib/mysql
      - ./db:/opt/db
    ports:
      - "3306:3306"
    restart: unless-stopped
    command: --default-authentication-plugin=mysql_native_password --log-bin-trust-function-creators=1

  wordpress:
    image: wordpress:php8.1
    init: true
    volumes:
      - ./public_html/dbv:/var/www/html/dbv
      - ./public_html/nraa-debug.php:/var/www/html/nraa-debug.php
      - ./public_html/wp-content/themes/nraa:/var/www/html/wp-content/themes/nraa
      - ./public_html/wp-content/plugins:/var/www/html/wp-content/plugins
      - ./public_html/nraa-calculate-grades.php:/var/www/html/nraa-calculate-grades.php
      - ./public_html/nraa-export.php:/var/www/html/nraa-export.php
      - ./public_html/nraa-import-csv.php:/var/www/html/nraa-import-csv.php
      - ./public_html/nraa-update-server.php:/var/www/html/nraa-update-server.php
      - ./public_html/wp-change-domain.php:/var/www/html/wp-change-domain.php
      - ./config/wp-config-dev.php:/var/www/html/wp-config.php
      - ./public_html/.user.ini:/var/www/html/.user.ini
      - ./tools:/opt/tools
    ports:
      - "8000:80"
    environment:
      WORDPRESS_DB_HOST: database
      WORDPRESS_DB_USER: root
      WORDPRESS_DB_PASSWORD: password
      WORDPRESS_DB_NAME: nraa_wp
      WORDPRESS_DEBUG: 1
    restart: unless-stopped

  adminer:
    image: adminer
    ports:
      - "8080:8080"
    depends_on:
      - database

  devcontainer:
    build:
      context: .
      dockerfile: .devcontainer/Dockerfile
    command: sleep infinity
    volumes:
      - ..:/workspaces:cached

volumes:
  mysql-data:
