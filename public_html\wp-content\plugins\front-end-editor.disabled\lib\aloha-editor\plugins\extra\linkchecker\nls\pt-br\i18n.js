define({
	"error.0": "Um erro ocorreu checando a URL...",
	"error.400": "Solicitação Impropria. A solicitação não pôde ser cumprida devido à sintaxe errada.",
	"error.401": "Não autorizado. Autenticação falhou ou não foi bem fornecida.",
	"error.402": "Pagamento requirido.",
	"error.403": "Proibido. O pedido foi legal, mas o serveidor está recusando a responde-la.",
	"error.404": "Não encontrado. O pedido solicitado não foi encontrado, mas pode estar disponível no futuro.",
	"error.405": "Método não permitido.",
	"error.406": "Não aceitável. Seu navegador não suporta o conteúdo.",
	"error.407": "Autenticação de proxy requirida.",
	"error.408": "Pedido expirado. O servidor expirou à espera do pedido.",
	"error.409": "Conflito no pedido.",
	"error.410": "Esse recurso desapareceu.",
	"error.411": "Comprimento requerido pelo servidor. Este link pode trabalhar em todos os navegadores.",
	"error.412": "Pré-requisito falhou. Esta ligação pode trabalhar em todos os navegadores.",
	"error.413": "Entidade solicitada muito grande. O pedido é maior do que o servidor está disposto ou capaz de processar.",
	"error.414": "Pedido URI Muito longo. O URI fornecido foi muito longo para o servidor para processar.",
	"error.415": "Tipo mídia não suportada. A entidade tem pedido um tipo de mídia que o servidor ou recurso não suporta.",
	"error.416": "Faixa solicitada não satisfatória. O cliente pediu uma parte do arquivo, mas o servidor não pode fornecer essa parte.",
	"error.417": "Probabilidade de falha. O servidor não pode satisfazer as exigências de seu navegador.",
	"error.418": "Eu sou um bule. ;-)",
	"error.500": "Erro do servidor interno. Uma mensagem de erro genérico, quando nenhuma mensagem específica é adequada.",
	"error.501": "Não implementado. O servidor não reconhecer o método de solicitação, ou não tem capacidade para atender a solicitação.",
	"error.502": "Bad Gateway. O servidor estava agindo como um gateway ou proxy e recebeu uma resposta inválida do servidor upstream.",
	"error.503": "Serviço não disponível. O servidor está indisponível no momento (porque está sobrecarregado ou em manutenção). Em geral, este é um estado temporário.",
	"error.504": "Gateway Timeout. O servidor estava agindo como um gateway ou proxy e não recebeu uma resposta rápida do servidor upstream.",
	"error.505": "Versão HTTP não suportada. O servidor não suporta a versão do protocolo HTTP usado no pedido."
});
