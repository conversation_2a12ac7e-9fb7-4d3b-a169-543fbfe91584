CREATE TABLE `events_ranges` (
  `event_id` int(10) unsigned NOT NULL,
  `range_id` int(10) unsigned NOT NULL,
  UNIQUE KEY `range_id` (`range_id`),
  KEY `event_id` (`event_id`),
  CONSTRAINT `events_ranges_ibfk_7` FOR<PERSON><PERSON>N KEY (`event_id`) REFERENCES `events` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
  CONSTRAINT `events_ranges_ibfk_8` FOREIGN KEY (`range_id`) REFERENCES `ranges` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=latin1