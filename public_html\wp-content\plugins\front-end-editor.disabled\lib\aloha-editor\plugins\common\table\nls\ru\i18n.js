define({
	"floatingmenu.tab.table": "Таблица",
	"floatingmenu.tab.tablelayout": "Разметка таблицы",
	"deleterows.confirm": "Вы действительно хотите удалить выбранные строки?",
	"deletecolumns.confirm": "Вы действительно хотите удалить выбранные колонки?",
	"deletetable.confirm": "Вы действительно хотите удалить таблицу?",
	"Table": "Таблица",
	"button.createtable.tooltip": "Вставить Таблицу",
	"button.addcolleft.tooltip": "Добавить колонку слева",
	"button.addcolright.tooltip": "Добавить колонку справа",
	"button.delcols.tooltip": "Удалить Колонки",
	"button.addrowbefore.tooltip": "Удалить пердыдущую строку",
	"button.addrowafter.tooltip": "Удалть следующую строку",
	"button.delrows.tooltip": "Удалить Строки",
	"button.caption.tooltip": "Заголовок таблицы",
	"empty.caption": "Заголовок таблицы",
	"button.removeFormat.tooltip": "Удалить форматирование",
	"button.removeFormat.text": "Удалить форматирование",
	"button.rowheader.tooltip": "Форматировать строку как заголовок таблицы",
	"button.columnheader.tooltip": "Форматировать столбец как заголовок таблицы",
	"button.mergecells.tooltip": "Объединить ячейки",
	"button.splitcells.tooltip": "Разбить ячейки",
	"table.label.target": "Резюме",
	"table.sidebar.title": "Таблица",
	"table.mergeCells.notRectangular": "Только прямоугольное выделение может быть объединено",
	"table.addColumns.nonConsecutive": "Пожалуйста, выберите одну колонку или диапазон колонок подряд",
	"table.createTable.nestedTablesNoSupported": "Извините, вложенные таблицы не поддерживаются"
});
