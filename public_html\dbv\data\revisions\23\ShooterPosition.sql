-- Changes

SET NAMES utf8;
SET time_zone = '+10:00';

USE `nraa_data`;

DROP TABLE IF EXISTS `event_squads`;
CREATE TABLE `event_squads` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `event_id` int(10) unsigned NOT NULL,
  `subevent_id` int(10) unsigned DEFAULT NULL,
  `number` int(11) NOT NULL,
  `start_target` int(11) NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `event_id_subevent_id_number` (`event_id`,`subevent_id`,`number`),
  KEY `subevent_id` (`subevent_id`),
  CONSTRAINT `event_squads_ibfk_1` FOREIGN KEY (`event_id`) REFERENCES `events` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
  CONSTRAINT `event_squads_ibfk_2` FOREIGN KEY (`subevent_id`) REFERENCES `subevents` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=latin1;


DROP TABLE IF EXISTS `event_squads_settings`;
CREATE TABLE `event_squads_settings` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `event_id` int(10) unsigned NOT NULL,
  `subevent_id` int(10) unsigned DEFAULT NULL,
  `start_target` int(11) NOT NULL DEFAULT '1',
  `end_target` int(11) NOT NULL DEFAULT '30',
  `is_group_left` tinyint(1) NOT NULL DEFAULT '0',
  `is_group_fclass` tinyint(1) NOT NULL DEFAULT '0',
  `allocate_by` enum('entry','discipline') NOT NULL DEFAULT 'entry',
  PRIMARY KEY (`id`),
  UNIQUE KEY `event_id_subevent_id` (`event_id`,`subevent_id`),
  KEY `event_id` (`event_id`),
  KEY `subevent_id` (`subevent_id`),
  CONSTRAINT `event_squads_settings_ibfk_1` FOREIGN KEY (`event_id`) REFERENCES `events` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
  CONSTRAINT `event_squads_settings_ibfk_2` FOREIGN KEY (`subevent_id`) REFERENCES `subevents` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=latin1;


DROP TABLE IF EXISTS `event_squads_shooters`;
CREATE TABLE `event_squads_shooters` (
  `event_squad_id` int(10) unsigned NOT NULL,
  `shooter_id` int(10) unsigned NOT NULL,
  `position` int(10) unsigned NOT NULL,
  UNIQUE KEY `event_shooter` (`event_squad_id`,`shooter_id`),
  KEY `event_squad` (`event_squad_id`),
  KEY `shooter` (`shooter_id`),
  CONSTRAINT `event_squads_shooters_ibfk_1` FOREIGN KEY (`event_squad_id`) REFERENCES `event_squads` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
  CONSTRAINT `event_squads_shooters_ibfk_2` FOREIGN KEY (`shooter_id`) REFERENCES `shooters` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=latin1;


DROP TABLE IF EXISTS `event_targets`;
CREATE TABLE `event_targets` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `event_squads_setting_id` int(10) unsigned NOT NULL,
  `name` varchar(255) NOT NULL,
  `is_enabled` tinyint(1) NOT NULL DEFAULT '0',
  `order` int(10) unsigned NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `event_squads_setting_id_order` (`event_squads_setting_id`,`order`),
  KEY `event_squads_setting_id` (`event_squads_setting_id`),
  CONSTRAINT `event_targets_ibfk` FOREIGN KEY (`event_squads_setting_id`) REFERENCES `event_squads_settings` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=latin1;


-- 2016-10-01 17:51:44
