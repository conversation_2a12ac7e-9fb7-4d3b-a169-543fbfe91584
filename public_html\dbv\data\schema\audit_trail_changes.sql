CREATE TABLE `audit_trail_changes` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `audit_trail_id` int(10) unsigned NOT NULL,
  `column_name` varchar(32) NOT NULL,
  `from` text,
  `to` text,
  PRIMARY KEY (`id`),
  UNIQUE KEY `audit_trail_id_column_name` (`audit_trail_id`,`column_name`),
  KEY `audit_trail` (`audit_trail_id`),
  CONSTRAINT `audit_trail_changes_ibfk_1` FOREIGN KEY (`audit_trail_id`) REFERENCES `audit_trails` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=latin1