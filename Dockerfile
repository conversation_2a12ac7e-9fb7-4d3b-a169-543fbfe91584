FROM php:8.1-fpm-alpine

RUN apk add --no-cache \
  freetype \
  libpng \
  libjpeg-turbo \
  freetype-dev \
  libpng-dev \
  libjpeg-turbo-dev \
  libxml2 \
  libxml2-dev \
    && docker-php-ext-configure gd \
      --with-freetype \
      --with-jpeg \
    && docker-php-ext-install -j$(getconf _NPROCESSORS_ONLN) gd \
    && docker-php-ext-install \
      pcntl \
      xml \
      mysqli \
      opcache \
    && docker-php-ext-enable opcache \
    && apk del --no-cache freetype-dev libpng-dev libjpeg-turbo-dev

COPY ./config/zz-log.conf /usr/local/etc/php-fpm.d/zz-log.conf
COPY ./tools/docker-bootstrap.sh /opt/docker-bootstrap.sh
COPY ./tools/install-composer.sh /opt/install-composer.sh

RUN /bin/sh /opt/install-composer.sh \
  && mv /var/www/html/composer.phar /usr/bin/

VOLUME ["/var/www/html"]
EXPOSE 9000

CMD /opt/docker-bootstrap.sh
