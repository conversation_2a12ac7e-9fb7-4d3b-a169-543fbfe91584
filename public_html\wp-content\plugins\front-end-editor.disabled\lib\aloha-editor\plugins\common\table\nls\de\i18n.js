define({
	"floatingmenu.tab.table": "Tabelle",
	"floatingmenu.tab.tablelayout": "Tabellenlayout",
	"deleterows.confirm": "Wollen Sie die ausgewählten Zeilen wirklich löschen?",
	"deletecolumns.confirm": "Wollen Sie die ausgewählten Spalten wirklich löschen?",
	"deletetable.confirm": "Wollen Sie wirklich die ganze Tabelle löschen?",
	"Table": "Tabelle",
	"button.createtable.tooltip": "Tabelle einfügen",
	"button.addcolleft.tooltip": "Spalte links einfügen",
	"button.addcolright.tooltip": "Spalte rechts einfügen",
	"button.delcols.tooltip": "Spalten löschen",
	"button.addrowbefore.tooltip": "Zeile darüber einfügen",
	"button.addrowafter.tooltip": "Zeile darunter einfügen",
	"button.delrows.tooltip": "Zeilen löschen",
	"button.caption.tooltip": "Tabellen Bezeichnung",
	"button.naturalfit.tooltip": "Spaltengrößen zurücksetzen",
	"empty.caption": "Tabellen Bezeichnung",
	"button.removeFormat.tooltip": "Formatierung entfernen",
	"button.removeFormat.text": "Formatierung entfernen",
	"button.rowheader.tooltip": "Reihe als Tabellenkopf formatieren",
	"button.columnheader.tooltip": "Spalte als Tabellenkopf formatieren",
	"button.mergecells.tooltip": "Zellen verknüpfen",
	"button.splitcells.tooltip": "Zellen teilen",
	"table.label.target": "Zusammenfassung",
	"table.sidebar.title": "Tabelle",
	"table.mergeCells.notRectangular": "Es kann nur eine rechteckige Auswahl verknüpft werden",
	"table.addColumns.nonConsecutive": "Bitte einzelne oder aufeinanderfolgende Reihen auswählen",
	"table.createTable.nestedTablesNoSupported": "Verschachtelte Tabellen werden nicht unterstützt"
});
