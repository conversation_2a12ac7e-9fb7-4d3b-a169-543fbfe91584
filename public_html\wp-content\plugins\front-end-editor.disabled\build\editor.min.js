(function(){var t,e,r,n,o,i,a,s,u,p,d,c,_,l,h,f={}.hasOwnProperty,y=function(t,e){function r(){this.constructor=t}for(var n in e)f.call(e,n)&&(t[n]=e[n]);return r.prototype=e.prototype,t.prototype=new r,t.__super__=e.prototype,t},m=[].indexOf||function(t){for(var e=0,r=this.length;r>e;e++)if(e in this&&this[e]===t)return e;return-1};n=function(t){var e,r,n,o,i,a;for(r={},a=t.attributes,o=0,i=a.length;i>o;o++)if(e=a[o],e.specified&&0===e.name.indexOf("data-")){n=e.value;try{n=jQuery.parseJSON(n)}catch(s){}null===n&&(n=""),r[e.name.substr(5)]=n}return r},jQuery.extend(FrontEndEditor,{fieldTypes:{},overlay:function(){var t;return t=jQuery("<div>",{"class":"fee-loading"}).css("background-image","url("+FrontEndEditor.data.spinner+")").hide().prependTo(jQuery("body")),{cover:function(e){var r,n,o,i,a;for(a=e.parents(),o=0,i=a.length;i>o&&(n=a[o],r=jQuery(n).css("background-color"),"transparent"===r);o++);return t.css({width:e.outerWidth(),height:e.outerHeight(),"background-color":r}).css(e.offset()).show()},hide:function(){return t.hide()}}}(),init_fields:function(){var t,e,r,n,o,i,a,s,u,p,d,c,_;for(d=jQuery(".fee-group").not(".fee-initialized"),a=0,u=d.length;u>a;a++)o=d[a],t=jQuery(o),e=t.find(".fee-field").removeClass("fee-field"),e.length&&(n=function(){var t,n,i;for(i=[],t=0,n=e.length;n>t;t++)o=e[t],r=FrontEndEditor.make_editable(o),r.part_of_group=!0,i.push(r);return i}(),i=t.hasClass("status-auto-draft")?"createPost":"group",r=new FrontEndEditor.fieldTypes[i](t,n),r.init_hover(t),t.data("fee-editor",r));for(c=jQuery(".fee-field").not(".fee-initialized"),_=[],s=0,p=c.length;p>s;s++)o=c[s],_.push(FrontEndEditor.make_editable(o,!0));return _},make_editable:function(t,e){var r,o,i,a;return r=jQuery(t),o=n(t),r.addClass("fee-initialized"),(a=FrontEndEditor.fieldTypes[o.type])?(i=new a,i.el=r,i.data=o,e&&(i.init_hover(r),r.data("fee-editor",i)),i):(console&&console.warn("invalid field type",t),void 0)}}),e=2,t=4,r=2,FrontEndEditor.controls=function(){function t(t){this.container=t}return t.prototype.not_editing=function(t){return this.container.html(t)},t.prototype.editing=function(t){return this.container.html(t)},t}(),FrontEndEditor.hover=function(){function n(t){var r=this;this.target=t,this.target.width()>this.target.parent().width()&&this.target.css("display","block"),this.border=jQuery("<div>",{"class":"fee-hover-border",css:{width:e}}).hide().appendTo("body"),this.container=jQuery("<div>",{"class":"fee-hover-container"}).hide().appendTo("body"),this.container.click(function(t){return t.preventDefault(),r.hide_immediately()}),this.target.mousemove(function(t){return r.position_vert(t.pageY)}),this.target.mouseover(function(t){return r.show(t.pageY)})}return n.prototype.lock=!1,n.prototype.timeout=null,n.prototype.not_editing=function(t){var e=this;return this.container.html(t),this.container.bind("mouseover.autohide",function(){return e.lock=!0}),this.container.bind("mouseout.autohide",function(){return e.lock=!1,e.hide()}),this.target.bind("mouseout.autohide",function(){return e.hide()})},n.prototype.editing=function(t,e){return this.container.html(t),this.target.unbind(".autohide"),this.container.unbind(".autohide"),this.show(e)},n.prototype.hide_immediately=function(){return this.container.hide(),this.border.hide()},n.prototype.hide=function(){var t=this;return this.timeout=setTimeout(function(){return t.lock?void 0:t.hide_immediately()},300)},n.prototype.position_vert=function(t){var r;return r=null!=t?t-this.container.outerHeight()/2-e:this.target.offset().top-2*e,this.container.css("top",r+"px")},n.prototype.show=function(n){var o;return this.position_vert(n),o=this.target.offset(),clearTimeout(this.timeout),this.container.css("left",o.left-this.container.outerWidth()-t-e+"px"),this.container.show(),this.border.css({left:o.left-t-e+"px",top:o.top-r-e+"px",height:this.target.outerHeight()+2*r+"px"}).show()},n}(),jQuery(document).ready(function(){var t,e,r,n,o,i;for(i=jQuery('[data-filter="widget_title"], [data-filter="widget_text"]'),n=0,o=i.length;o>n;n++)r=i[n],t=jQuery(r),e=t.closest(".widget_text"),e.length?(t.attr("data-widget_id",e.attr("id")),e.addClass("fee-group")):t.unwrap();return FrontEndEditor.init_fields()}),jQuery(window).load(function(){var t;return null!=(t=jQuery(".fee-group.status-auto-draft").data("fee-editor"))?t.start_editing():void 0}),FrontEndEditor.fieldTypes.base=function(){function t(){}return t.prototype.el=null,t.prototype.get_type=function(){return this.constructor.name},t.prototype.pre_edit_button=function(){var t=this;return jQuery("<button>",{"class":"fee-hover-edit",html:FrontEndEditor.data.edit_text,click:function(e){return t.last_mouse_pos=e.pageY,t.start_editing()}})},t.prototype.start_editing=null,t.prototype.init_hover=function(t){return this.hover=new FrontEndEditor.hover(t),this.hover.not_editing(this.pre_edit_button())},t.prototype.ajax_get=function(){var t=this;return this.el.trigger("edit_start"),this._ajax_request({data:this.ajax_get_args.apply(this,arguments),success:function(){return t.ajax_get_handler.apply(t,arguments),t.el.trigger("edit_started")}})},t.prototype.ajax_set=function(){var t=this;return this.el.trigger("edit_save"),this._ajax_request({data:this.ajax_set_args.apply(this,arguments),success:function(){return t.ajax_set_handler.apply(t,arguments),t.el.trigger("edit_saved")}})},t.prototype._ajax_request=function(t){return t.url=FrontEndEditor.data.ajax_url,t.type="POST",t.dataType="json",jQuery.ajax(t)},t.prototype.ajax_get_handler=null,t.prototype.ajax_set_handler=null,t.prototype.ajax_get_args=function(){var t;return t=this.ajax_args(),t.callback="get",t},t.prototype.ajax_set_args=function(t){var e;return e=this.ajax_args(),e.callback="save",e.content=t,e},t.prototype.ajax_args=function(){return{action:"front-end-editor",nonce:FrontEndEditor.data.nonce,data:this.data}},t}(),FrontEndEditor.fieldTypes.input=function(t){function e(){return o=e.__super__.constructor.apply(this,arguments)}return y(e,t),e.prototype.input_tag='<input type="text">',e.prototype.start_editing=function(){return this.create_form(),this.create_input(),this.ajax_get()},e.prototype.create_buttons=function(){var t=this;return this.save_button=jQuery("<button>",{"class":"fee-form-save",text:FrontEndEditor.data.save_text,click:function(){return t.ajax_set()}}),this.cancel_button=jQuery("<button>",{"class":"fee-form-cancel",text:FrontEndEditor.data.cancel_text,click:function(){return t.remove_form()}}),this.save_button.add(this.cancel_button)},e.prototype.create_form=function(){var t=this;return this.form=jQuery(this.el.is("span")?"<span>":"<div>").addClass("fee-form").addClass("fee-type-"+this.get_type()),this.form.keypress(function(e){return t.keypress(e.keyCode||e.which||e.charCode||0)})},e.prototype.remove_form=function(){return this.form.remove(),this.el.show()},e.prototype.keypress=function(t){var e;return e={ENTER:13,ESCAPE:27},t===e.ENTER&&"input"===this.get_type()&&this.save_button.click(),t===e.ESCAPE?this.cancel_button.click():void 0},e.prototype.create_input=function(){return this.input=jQuery(this.input_tag).attr({id:"fee-"+(new Date).getTime(),"class":"fee-form-content"}),this.input.prependTo(this.form)},e.prototype.content_to_input=function(t){return this.input.val(t)},e.prototype.content_from_input=function(){return this.input.val()},e.prototype.content_to_front=function(t){return this.el.html(t)},e.prototype.ajax_get=function(){return FrontEndEditor.overlay.cover(this.el),e.__super__.ajax_get.apply(this,arguments)},e.prototype.ajax_set_args=function(t){return FrontEndEditor.overlay.cover(this.form),0===arguments.length&&(t=this.content_from_input()),e.__super__.ajax_set_args.call(this,t)},e.prototype.ajax_get_handler=function(t){var e;return(e=this.error_handler(t))?(this.el.hide(),this.form.insertAfter(e),this.content_to_input(t.content),this.input.focus(),this.part_of_group?void 0:this.show_control_buttons()):void 0},e.prototype.show_control_buttons=function(){var t;return t=new FrontEndEditor.hover(this.form),t.editing(this.create_buttons(),this.last_mouse_pos)},e.prototype.ajax_set_handler=function(t){var e;return(e=this.error_handler(t))?(this.content_to_front(t.content),this.remove_form()):void 0},e.prototype.error_handler=function(t){var e,r;return r=this.el.closest("a"),e=r.length?r:this.el,FrontEndEditor.overlay.hide(),t.error?(jQuery('<div class="fee-error">').append(jQuery('<span class="fee-message">').html(t.error)).append(jQuery('<span class="fee-dismiss">x</span>').click(function(){return $error_box.remove()})).insertBefore(e),!1):e},e}(FrontEndEditor.fieldTypes.base),FrontEndEditor.fieldTypes.select=function(t){function e(){return i=e.__super__.constructor.apply(this,arguments)}return y(e,t),e.prototype.input_tag="<select>",e.prototype.content_to_input=function(t){var e,r,n;n=this.data.values;for(r in n)f.call(n,r)&&(e=n[r],this.input.append(jQuery("<option>",{value:r,html:e,selected:t===r})));return!1},e.prototype.content_from_input=function(){return this.input.find(":selected").val()},e}(FrontEndEditor.fieldTypes.input),FrontEndEditor.fieldTypes.textarea=function(t){function e(){return s=e.__super__.constructor.apply(this,arguments)}return y(e,t),e.prototype.input_tag='<textarea rows="10">',e}(FrontEndEditor.fieldTypes.input),FrontEndEditor.fieldTypes.image_base=function(t){function e(){return u=e.__super__.constructor.apply(this,arguments)}var r;return y(e,t),e.prototype.button_text=null!=(r=FrontEndEditor.data.image)?r.change:void 0,e.prototype.start_editing=function(){var t=this;return tb_show(this.button_text,FrontEndEditor.data.image.url),jQuery("#TB_closeWindowButton img").attr("src",FrontEndEditor.data.image.tb_close),jQuery("#TB_iframeContent").load(function(e){var r,n;return n=e.currentTarget.contentWindow,r=n.jQuery(n.document),t.thickbox_load(r),jQuery.noop!==t.media_item_manipulation?(r.find(".media-item").each(function(e,r){return t.media_item_manipulation(n.jQuery(r))}),r.ajaxComplete(function(e,n){var o;return o=jQuery(n.responseText).find(".media-item-info").attr("id"),t.media_item_manipulation(r.find("#"+o).closest(".media-item"))})):void 0})},e.prototype.thickbox_load=function(t){var e=this;return t.delegate(".media-item :submit","click",function(t){var r,n;return r=jQuery(t.currentTarget),n=r.closest("form").serializeArray(),n.push({name:r.attr("name"),value:r.attr("name")}),n.push({name:"action",value:"fee_image_insert"}),jQuery.post(FrontEndEditor.data.ajax_url,n,function(t){return e.image_html_handler(t)}),!1})},e.prototype.media_item_manipulation=function(t){return t.find("#go_button").remove(),t.find(":submit").val(this.button_text)},e}(FrontEndEditor.fieldTypes.base),FrontEndEditor.fieldTypes.image=function(t){function e(){return p=e.__super__.constructor.apply(this,arguments)}return y(e,t),e.prototype.start_editing=function(){var t=this;return e.__super__.start_editing.apply(this,arguments),jQuery('<a id="fee-img-revert" href="#">').text(FrontEndEditor.data.image.revert).click(function(){return t.ajax_set(-1),!1}).insertAfter("#TB_ajaxWindowTitle")},e.prototype.media_item_manipulation=function(t){return t.find("tbody tr").not(".image-size, .submit").hide(),e.__super__.media_item_manipulation.apply(this,arguments)},e.prototype.image_html_handler=function(t){var e;return e=jQuery(t),e.is("a")&&(e=e.find("img")),this.ajax_set(e.attr("src"))},e.prototype.ajax_set_handler=function(t){var e;return e=t.content,"-1"===e?location.reload(!0):(this.el.find("img").attr("src",e),tb_remove())},e}(FrontEndEditor.fieldTypes.image_base),FrontEndEditor.fieldTypes.thumbnail=function(t){function e(){return d=e.__super__.constructor.apply(this,arguments)}return y(e,t),e.prototype.thickbox_load=function(t){var e=this;return t.find("#tab-type_url").remove(),t.delegate(".media-item :submit","click",function(t){var r,n;return r=jQuery(t.currentTarget).closest(".media-item").find(".media-item-info"),n=r.attr("id").replace("media-head-",""),e.ajax_set(n),!1})},e.prototype.media_item_manipulation=function(t){return t.find("tbody tr").not(".submit").remove(),e.__super__.media_item_manipulation.apply(this,arguments)},e}(FrontEndEditor.fieldTypes.image),"undefined"!=typeof Aloha&&null!==Aloha&&Aloha.require(["aloha/selection"],function(t){var e;return FrontEndEditor.fieldTypes.image_rich=function(r){function n(){return e=n.__super__.constructor.apply(this,arguments)}var o;return y(n,r),n.prototype.button_text=null!=(o=FrontEndEditor.data.image)?o.insert:void 0,n.prototype.start_editing=function(){return jQuery(".aloha-floatingmenu, #aloha-floatingmenu-shadow").hide(),n.__super__.start_editing.apply(this,arguments)},n.prototype.media_item_manipulation=jQuery.noop,n.prototype.image_html_handler=function(e){return GENTICS.Utils.Dom.insertIntoDOM(jQuery(e),t.getRangeObject(),Aloha.activeEditable.obj),tb_remove(),jQuery(".aloha-floatingmenu, #aloha-floatingmenu-shadow").show()},n}(FrontEndEditor.fieldTypes.image_base)}),FrontEndEditor.fieldTypes.rich=function(t){function e(){return c=e.__super__.constructor.apply(this,arguments)}return y(e,t),e.prototype.content_from_input=function(){return Aloha.getEditableById(this.form.attr("id")).getContents()},e.prototype.create_input=jQuery.noop,e.prototype.create_form=function(){return this.form=Aloha.jQuery('<div class="fee-form fee-type-rich">')},e.prototype.remove_form=function(){return this.form.mahalo(),e.__super__.remove_form.apply(this,arguments)},e.prototype.ajax_get_handler=function(t){var e;return(e=this.error_handler(t))?(this.form.html(t.content),this.el.hide(),this.form.insertAfter(e),this.form.aloha(),this.part_of_group?void 0:(this.show_control_buttons(),this.form.focus(),this.form.dblclick())):void 0},e}(FrontEndEditor.fieldTypes.textarea),FrontEndEditor.fieldTypes.terminput=function(t){function e(){return _=e.__super__.constructor.apply(this,arguments)}return y(e,t),e.prototype.content_to_input=function(){return e.__super__.content_to_input.apply(this,arguments),this.input.suggest(FrontEndEditor.data.ajax_url+"?action=ajax-tag-search&tax="+this.data.taxonomy,{multiple:!0,resultsClass:"fee-suggest-results",selectClass:"fee-suggest-over",matchClass:"fee-suggest-match"})},e}(FrontEndEditor.fieldTypes.input),FrontEndEditor.fieldTypes.termselect=function(t){function e(){return l=e.__super__.constructor.apply(this,arguments)}return y(e,t),e.prototype.content_to_input=function(t){var e;return e=jQuery(t),this.input.replaceWith(e),this.input=e},e}(FrontEndEditor.fieldTypes.select),FrontEndEditor.fieldTypes.widget=function(t){function e(){return h=e.__super__.constructor.apply(this,arguments)}return y(e,t),e.prototype.create_input=jQuery.noop,e.prototype.content_to_input=function(t){return this.input=jQuery(t),this.form.prepend(t)},e.prototype.ajax_set_args=function(){var t,r,n,o,i,a,s;for(t=e.__super__.ajax_set_args.apply(this,arguments),a=this.form.find(":input").serializeArray(),o=0,i=a.length;i>o;o++)s=a[o],r=s.name,n=s.value,t[r]=void 0===t[r]?n:jQuery.isArray(t[r])?t[r].concat(n):[t[r],n];return t},e}(FrontEndEditor.fieldTypes.textarea),FrontEndEditor.fieldTypes.group=function(t){function e(t,r){this.el=t,this.editors=r,e.__super__.constructor.apply(this,arguments)}return y(e,t),e.prototype.create_input=jQuery.noop,e.prototype.init_hover=function(t){var r;return r=t.find(".fee-buttons"),r.length?(this.hover=new FrontEndEditor.controls(r),this.hover.not_editing(this.pre_edit_button())):e.__super__.init_hover.apply(this,arguments)},e.prototype.create_form=function(){var t,e,r,n;for(n=this.editors,e=0,r=n.length;r>e;e++)t=n[e],t.create_form(),t.create_input();return this.form=this.el},e.prototype.remove_form=function(){var t,e,r,n;for(n=this.editors,e=0,r=n.length;r>e;e++)t=n[e],t.remove_form();return this.hover.not_editing(this.pre_edit_button())},e.prototype.content_from_input=function(){var t,e,r,n,o;for(n=this.editors,o=[],e=0,r=n.length;r>e;e++)t=n[e],o.push(t.content_from_input());return o},e.prototype.keypress=jQuery.noop,e.prototype.ajax_set=function(){return e.__super__.ajax_set.apply(this,arguments),FrontEndEditor.overlay.cover(this.el)},e.prototype.ajax_args=function(){var t,r,n,o,i,a,s,u,p,d,c;if(t=e.__super__.ajax_args.apply(this,arguments),t.group=!0,o=function(){var t,e,r,n;for(r=this.editors,n=[],t=0,e=r.length;e>t;t++)i=r[t],n.push(i.data);return n}.call(this),1===o.length)t.data=o;else{for(r=jQuery.extend({},o[0]),a=d=1,c=o.length;c>=1?c>d:d>c;a=c>=1?++d:--d)for(u in r)f.call(r,u)&&(p=r[u],p!==o[a][u]&&delete r[u]);t.data=function(){var t,e,i;for(i=[],t=0,e=o.length;e>t;t++){n=o[t],s={};for(u in n)f.call(n,u)&&0>m.call(r,u)&&(s[u]=n[u]);i.push(s)}return i}(),t.commonData=r}return t},e.prototype.ajax_get_handler=function(t){var e,r,n,o,i,a;for(i=this.editors,r=n=0,o=i.length;o>n;r=++n)e=i[r],e.ajax_get_handler(t[r]);return null!=(a=this.editors[0].input)&&a.focus(),this.hover.editing(this.create_buttons(),this.last_mouse_pos)},e.prototype.ajax_set_handler=function(t){var e,r,n,o,i;for(i=this.editors,r=n=0,o=i.length;o>n;r=++n)e=i[r],e.ajax_set_handler(t[r]);return this.remove_form()},e}(FrontEndEditor.fieldTypes.input),FrontEndEditor.fieldTypes.createPost=function(t){function e(){return a=e.__super__.constructor.apply(this,arguments)}return y(e,t),e.prototype.ajax_set_args=function(){var t;return t=e.__super__.ajax_set_args.apply(this,arguments),t.createPost=!0,t},e.prototype.ajax_set_handler=function(t){return window.location=t.permalink},e}(FrontEndEditor.fieldTypes.group)}).call(this);