define({
	"button.strong.tooltip": "<PERSON><PERSON>",
	"button.em.tooltip": "Destacar",
	"button.b.tooltip": "Negrito",
	"button.i.tooltip": "Itálico",
	"button.u.tooltip": "Sublinhado",
	"button.cite.tooltip": "Citar",
	"button.q.tooltip": "Dica",
	"button.code.tooltip": "<PERSON>ódigo",
	"button.abbr.tooltip": "Abreviação",
	"button.del.tooltip": "Tachado",
	"button.s.tooltip": "Tachado",
	"button.sub.tooltip": "Subscrito",
	"button.sup.tooltip": "Sobrescrito",
	"button.p.tooltip": "Parágrafo",
	"button.h1.tooltip": "Cabeçalho 1",
	"button.h2.tooltip": "Cabeçalho 2",
	"button.h3.tooltip": "Cabeçalho 3",
	"button.h4.tooltip": "Cabeçalho 4",
	"button.h5.tooltip": "Cabeçalho 5",
	"button.h6.tooltip": "Cabeçalho 6",
	"button.pre.tooltip": "Texto pré-formatado",
	"button.title.tooltip": "Título",
	"button.removeFormat.tooltip": "Remover formatação",
	"button.removeFormat.text": "Remover formatação",
	"GENTICS_button_p": "GENTICS_button_p",
	"GENTICS_button_h1": "GENTICS_button_h1",
	"GENTICS_button_h2": "GENTICS_button_h2",
	"GENTICS_button_h3": "GENTICS_button_h3",
	"GENTICS_button_h4": "GENTICS_button_h4",
	"GENTICS_button_h5": "GENTICS_button_h5",
	"GENTICS_button_h6": "GENTICS_button_h6",
	"GENTICS_button_pre": "GENTICS_button_pre",
	"GENTICS_button_title": "GENTICS_button_title",
	"formatBold": "Ctrl+B",
	"formatItalic": "Ctrl+I",
	"formatUnderline": "Ctrl+U",
	"formatParagraph": "Alt+Ctrl+0",
	"formatH1": "Alt+Ctrl+1",
	"formatH2": "Alt+Ctrl+2",
	"formatH3": "Alt+Ctrl+3",
	"formatH4": "Alt+Ctrl+4",
	"formatH5": "Alt+Ctrl+5",
	"formatH6": "Alt+Ctrl+6",
	"formatPre": "Alt+Ctrl+P",
	"formatDel": "Ctrl+\\",
	"formatSub": "Ctrl+,",
	"formatSup": "Ctrl+.",
	"floatingmenu.tab.format": "Formatação",
	"format.class.legend": "Classe CSS",
	"format.class.none": "none"
});
