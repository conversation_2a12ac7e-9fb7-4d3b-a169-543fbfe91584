CREATE TABLE `event_squads` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `event_id` int(10) unsigned NOT NULL,
  `subevent_id` int(10) unsigned DEFAULT NULL,
  `number` int(11) NOT NULL,
  `start_target` int(11) NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `event_id_subevent_id_number` (`event_id`,`subevent_id`,`number`),
  KEY `subevent_id` (`subevent_id`),
  CONSTRAINT `event_squads_ibfk_1` FOREIGN KEY (`event_id`) REFERENCES `events` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
  CONSTRAINT `event_squads_ibfk_2` FOREIGN KEY (`subevent_id`) REFERENCES `subevents` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=latin1