.aloha-comments-wrapper {
	display: inline;
}
.aloha-comments-hover,
.aloha-comments-active {
	-moz-box-shadow:	0 2px 10px rgba(0, 0, 0, .4);
	-webkit-box-shadow:	0 2px 10px rgba(0, 0, 0, .4);
	box-shadow:			0 2px 10px rgba(0, 0, 0, .4);
	color: #333;
	text-shadow: 0 0 1px rgba(0, 0, 0, .4);
	cursor: default;
}
.aloha-comments-clear {
	float: none;
	clear: both;
}
.aloha-comments-addbox {
	z-index: 2147483647;
	position: absolute;
	display: none;
	width: 300px;
	padding: 10px;
	color: #555;
	border: 1px solid #aaa;
	background-color: #fff;
	border-radius:			5px;
	-moz-border-radius:		5px;
	-webkit-border-radius:	5px;
	background-image: -webkit-gradient(
		linear,
		center top,
		center bottom,
		color-stop(0.00, #ffffff),
		color-stop(0.05, #ffffff),
		color-stop(0.80, #eeeeee),
		color-stop(0.90, #eaeaea),
		color-stop(1.00, #dddddd)
	);
	background-image: -moz-linear-gradient(
		center top,
		#ffffff 0%,
		#ffffff 05%,
		#eeeeee 80%,
		#eaeaea 90%,
		#dddddd 100%
	);
	filter: progid:DXImageTransform.Microsoft.Gradient(
		startColorstr='#ffffff',
		endColorstr='#eeeeee',
		gradientType='0'
	);
	-moz-box-shadow:	0 2px 5px rgba(0, 0, 0, .5);
	-webkit-box-shadow:	0 2px 5px rgba(0, 0, 0, .5);
	box-shadow:			0 2px 5px rgba(0, 0, 0, .5);
}
.aloha-comments-addbox-content {
	overflow: hidden;
	margin: 0 auto;
}
.aloha-comments-addbox input,
.aloha-comments-addbox textarea {
	width: 288px;
	height: 20px;
	margin-bottom: 10px;
	padding: 5px;
	color: #555;
	font-size: 16px;
	font-family: Arial;
	font-weight: bold;
	border: 1px solid #ccc;
	background: #fff url(../img/textbox.png) no-repeat -1px -1px;
	border-radius:			2px;
	-moz-border-radius:		2px;
	-webkit-border-radius:	2px;
}
.aloha-comments-addbox textarea {
	height: 100px;
}
.aloha-comments-addbox input.aloha-comments-error,
.aloha-comments-addbox textarea.aloha-comments-error {
	border-color: #f68;
}
.aloha-comments-addbox h2 {
	margin: 0 0 10px;
	padding: 0;
	font-size: 16px;
	text-shadow: 0 0 1px rgba(0, 0, 0, .4);
}


.aloha-comments-btn {
	float: right;
	padding: 5px 10px;
	border: 1px solid #aaa;
	
	border-radius:			2px;
	-moz-border-radius:		2px;
	-webkit-border-radius:	2px;
	background-image: -webkit-gradient(
		linear,
		center top,
		center bottom,
		color-stop(0.00, #fff),
		color-stop(0.10, #eee),
		color-stop(0.50, #eee),
		color-stop(0.90, #ddd),
		color-stop(1.00, #ccc)
	);
	background-image: -moz-linear-gradient(
		center top,
		#ffffff 0%,
		#ffffff 05%,
		#eeeeee 80%,
		#eaeaea 90%,
		#dddddd 100%
	);
	filter: progid:DXImageTransform.Microsoft.Gradient(
		startColorstr='#ffffff',
		endColorstr='#eeeeee',
		gradientType='0'
	);
	
	-moz-box-shadow:	0 0 3px rgba(0, 0, 0, .2);
	-webkit-box-shadow:	0 0 3px rgba(0, 0, 0, .2);
	box-shadow:			0 0 3px rgba(0, 0, 0, .2);
	
	color: #555;
	
	opacity: 0.8;
	filter: alpha(opacity=80);
	
	cursor: pointer;
	
	text-shadow: 0 0 1px rgba(0, 0, 0, .4);
}
.aloha-comments-btn:hover {
	-moz-box-shadow:	0 0 6px rgba(0, 0, 0, .4);
	-webkit-box-shadow:	0 0 6px rgba(0, 0, 0, .4);
	box-shadow:			0 0 6px rgba(0, 0, 0, .4);
	opacity: 1;
	filter: alpha(opacity=100);
}

.aloha-comments-colors {
	float: left;
}
.aloha-comments-colors li {
	float: left;
	list-style: none;
	display: block;
	width: 16px;
	height: 16px;
	margin-right: 5px;
	cursor: pointer;
	border: 1px solid rgba(0, 0, 0, .2);
	border-radius:			2px;
	-moz-border-radius:		2px;
	-webkit-border-radius:	2px;
}
.aloha-comments-colors li:hover {
	border-color: rgba(0, 0, 0, .4);
	-moz-box-shadow:	0 2px 5px rgba(0, 0, 0, .4);
	-webkit-box-shadow:	0 2px 5px rgba(0, 0, 0, .4);
	box-shadow:			0 2px 5px rgba(0, 0, 0, .4);
}

.aloha-comments-arrow,
.aloha-comments-arrow-inner {
	position: absolute;
	border-width: 15px;
	border-color: transparent;
	border-style: solid;
	height: 0;
	width: 0;
}
.aloha-comments-arrow {
	border-top-color: #aaa;
	margin: 10px 0 0 130px;
}
.aloha-comments-arrow-inner {
	border-top-color: #ddd;
	margin: -16px 0 0 -15px;
}
.aloha-comments-point-from-bottom .aloha-comments-arrow {
	top: 0px;
	border-top-color: transparent;
	border-bottom-color: #aaa;
	margin-top: -30px;
}
.aloha-comments-point-from-bottom .aloha-comments-arrow-inner {
	border-top-color: transparent;
	border-bottom-color: #fff;
	margin-top: -14px;
}


.aloha-comments-bar-inner {
	background-color: #222;
	background-image: -webkit-radial-gradient(
		rgba(50, 50, 50, 0.5) 0%,
		rgba(50, 50, 50, 0.6) 40%,
		rgba(0,  0,  0,  0.5) 100%
	);
	background-image: -moz-radial-gradient(
		rgba(50, 50, 50, 0.5),
		rgba(50, 50, 50, 0.6) 40%,
		rgba(0,  0,  0,  0.5)
	);
	border-right: 1px solid #333;
}
.aloha-comments-bar-toggle-img {
	width: 40px;
	height: 40px;
	background: url(../img/comments.png) no-repeat center center;
	float: right;
}
.aloha-comments-bar-toggle {
	text-align: right;
	display: block;
	width: 40px;
	height: 40px;
	background-color: #222;
	background-image: -webkit-gradient(
		linear,
		left center,
		right center,
		color-stop(0.00, rgba(0, 0, 0, .9)),
		color-stop(0.10, rgba(0, 0, 0, .5)),
		color-stop(0.30, rgba(0, 0, 0, .2)),
		color-stop(1.00, rgba(0, 0, 0, .0))
	);
	background-image: -moz-linear-gradient(
		center top,
		rgba(0, 0, 0, .8) 0%,
		rgba(0, 0, 0, .0) 10%,
		rgba(0, 0, 0, .0) 90%,
		rgba(0, 0, 0, .8) 100%
	);
	position: absolute;
	top: 20px;
	right: 20px;
	cursor: pointer;
	
	-moz-box-shadow:	0px 0px 5px rgba(0, 0, 0, .7);
	-webkit-box-shadow:	0px 0px 5px rgba(0, 0, 0, .7);
	box-shadow:			0px 0px 5px rgba(0, 0, 0, .7);
	
	border-top-right-radius:	 5px;
	-moz-border-radius-topright: 5px;
	-webkit-border-top-right-radius: 5px;
	-webkit-border-bottom-right-radius: 5px;
	
	opacity: 0.75;
	filter: alpha(opacity=75);
}
.aloha-comments-bar-toggle:hover {
	opacity: 1;
	filter: alpha(opacity=100);
	right: 15px;
	width: 45px;
}
.aloha-comments-bar-toggle-opened:hover {
	right: 25px;
	width: 35px;
}
.aloha-comments-bar-shadow {
	width: 5px;
	position: absolute;
	top: 0;
	left: 0px;
	background-image: -webkit-gradient(
		linear,
		left center,
		right center,
		color-stop(0.00, rgba(0, 0, 0, .9)),
		color-stop(0.10, rgba(0, 0, 0, .5)),
		color-stop(0.30, rgba(0, 0, 0, .2)),
		color-stop(1.00, rgba(0, 0, 0, .0))
	);
	background-image: -moz-linear-gradient(
		center top,
		rgba(0, 0, 0, .8) 0%,
		rgba(0, 0, 0, .0) 10%,
		rgba(0, 0, 0, .0) 90%,
		rgba(0, 0, 0, .8) 100%
	);
}
.aloha-comments-bar {
	z-index: 2147483647;
	overflow: hidden;
	position: fixed;
	top: 0;
	left: 0;
	width: 0;
	padding-right: 60px;
}
.aloha-comments-bar h2 {
	margin: 0;
	padding: 10px 0 10px 52px;
	background: url(../img/comments.png) no-repeat 10px center;
	border-bottom: 1px solid #111;
	border-color: rgba(0, 0, 0, .4);
	color: #999;
	text-shadow: 0 0 4px rgba(0, 0, 0, .8);
}
.aloha-comments-bar h2 span:hover {
	opacity: 1;
	filter: alpha(opacity=100);

}
.aloha-comments-bar ul {
	border-top: 1px solid #222;
	border-color: rgba(100, 100, 100, .4);
}
.aloha-comments-bar li {
	margin-bottom: 5px;
	padding: 10px;
	border-bottom: 1px solid rgba(127, 127, 127, .3);
	background-color: rgba(0, 0, 0, .4);
	background-image: -webkit-gradient(
		linear,
		center top,
		center bottom,
		color-stop(0.00, rgba(0, 0, 0, .8)),
		color-stop(0.10, rgba(0, 0, 0, .0)),
		color-stop(0.90, rgba(0, 0, 0, .0)),
		color-stop(1.00, rgba(0, 0, 0, .8))
	);
	background-image: -moz-linear-gradient(
		center top,
		rgba(0, 0, 0, .8) 0%,
		rgba(0, 0, 0, .0) 10%,
		rgba(0, 0, 0, .0) 90%,
		rgba(0, 0, 0, .8) 100%
	);
}
.aloha-comments-bar-bottom {
	height: 20px;
	background-image: -webkit-gradient(
		linear,
		center top,
		center bottom,
		color-stop(0.00, rgba(255, 255, 255, .1)),
		color-stop(0.20, rgba(255, 255, 255, .05)),
		color-stop(1.00, rgba(255, 255, 255, .0))
	);
	background-image: -moz-linear-gradient(
		center top,
		rgba(255, 255, 255, .1)	 0%,
		rgba(255, 255, 255, .05) 20%,
		rgba(255, 255, 255, .0)	 100%
	);
}
.aloha-comments-bar-comment {
	padding: 5px;
	background-color: #fe9;
	background-image: -webkit-gradient(
		linear,
		center top,
		center bottom,
		color-stop(0.00, rgba(255, 255, 255, .0)),
		color-stop(0.01, rgba(255, 255, 255, .4)),
		color-stop(0.20, rgba(255, 255, 255, .2)),
		color-stop(1.00, rgba(255, 255, 255, .0))
	);
	background-image: -moz-linear-gradient(
		center top,
		rgba(255, 255, 255, .1)	 0%,
		rgba(255, 255, 255, .05) 20%,
		rgba(255, 255, 255, .0)	 100%
	);
	border-radius:			3px;
	-moz-border-radius:		3px;
	-webkit-border-radius:	3px;
	color: #333;
	font-size: 13px;
}
.aloha-comments-bar-comment img {
	float: left;
	margin-right: 10px;
	padding: 2px;
	background: #fff;
	border: 1px solid #ccc;
}

.aloha-comments-bar-comment-active .aloha-comments-bar-comment,
.aloha-comments-bar-comment:hover {
	background-color: #fd4;
}
.aloha-comments-bar-comment span {
	display: block;
	margin-bottom: 5px;
	padding-bottom: 5px;
	background: url(../img/hr.png) repeat-x left bottom;
	font-size: 11px;
}
.aloha-comments-bar-comment li {
	background: transparent;
	border-width: 0;
	font-size: 30px;
}

.aloha-comments-bar-reply {
	overflow: hidden;
	height: 0;
	margin-top: 5px;
	padding-top: 15px;
	background: url(../img/hr.png) repeat-x left top;
}
.aloha-comments-bar-reply input,
.aloha-comments-bar-reply textarea {
	width: 100%;
	margin-bottom: 5px;
	padding: 5px;
	color: #555;
	font-size: 16px;
	font-family: Arial;
	font-weight: bold;
	border: 1px solid rgba(0, 0, 0, 0.2);
	background: #fff url(../img/textbox.png) no-repeat -1px -1px;
	border-radius:			2px;
	-moz-border-radius:		2px;
	-webkit-border-radius:	2px;
}
.aloha-comments-bar-reply textarea {
	height: 100px;
}


/**
 * Toolbar
 */
button.aloha-comments-toolbar-btn {
	width: 40px;
	height: 40px;
}
button.aloha-comments-btn-add {
	background: url(../img/add.png) no-repeat center center !important;
}
button.aloha-comments-btn-reveal {
	background: url(../img/comments.png) no-repeat center center !important;
}