// Generated by CoffeeScript 1.6.3
(function() {
  var HORIZONTAL_PADDING, HOVER_BORDER, VERTICAL_PADDING, extract_data_attr, _ref, _ref1, _ref10, _ref2, _ref3, _ref4, _ref5, _ref6, _ref7, _ref8, _ref9,
    __hasProp = {}.hasOwnProperty,
    __extends = function(child, parent) { for (var key in parent) { if (__hasProp.call(parent, key)) child[key] = parent[key]; } function ctor() { this.constructor = child; } ctor.prototype = parent.prototype; child.prototype = new ctor(); child.__super__ = parent.prototype; return child; },
    __indexOf = [].indexOf || function(item) { for (var i = 0, l = this.length; i < l; i++) { if (i in this && this[i] === item) return i; } return -1; };

  extract_data_attr = function(el) {
    var attr, data, value, _i, _len, _ref;
    data = {};
    _ref = el.attributes;
    for (_i = 0, _len = _ref.length; _i < _len; _i++) {
      attr = _ref[_i];
      if (attr.specified && attr.name.indexOf('data-') === 0) {
        value = attr.value;
        try {
          value = jQuery.parseJSON(value);
        } catch (_error) {}
        if (value === null) {
          value = '';
        }
        data[attr.name.substr(5)] = value;
      }
    }
    return data;
  };

  jQuery.extend(FrontEndEditor, {
    fieldTypes: {},
    overlay: (function() {
      var $cover;
      $cover = jQuery('<div>', {
        'class': 'fee-loading'
      }).css('background-image', 'url(' + FrontEndEditor.data.spinner + ')').hide().prependTo(jQuery('body'));
      return {
        cover: function($el) {
          var bgcolor, parent, _i, _len, _ref;
          _ref = $el.parents();
          for (_i = 0, _len = _ref.length; _i < _len; _i++) {
            parent = _ref[_i];
            bgcolor = jQuery(parent).css('background-color');
            if (bgcolor !== 'transparent') {
              break;
            }
          }
          return $cover.css({
            'width': $el.outerWidth(),
            'height': $el.outerHeight(),
            'background-color': bgcolor
          }).css($el.offset()).show();
        },
        hide: function() {
          return $cover.hide();
        }
      };
    })(),
    init_fields: function() {
      var $container, $elements, editor, editors, el, fieldType, _i, _j, _len, _len1, _ref, _ref1, _results;
      _ref = jQuery('.fee-group').not('.fee-initialized');
      for (_i = 0, _len = _ref.length; _i < _len; _i++) {
        el = _ref[_i];
        $container = jQuery(el);
        $elements = $container.find('.fee-field').removeClass('fee-field');
        if (!$elements.length) {
          continue;
        }
        editors = (function() {
          var _j, _len1, _results;
          _results = [];
          for (_j = 0, _len1 = $elements.length; _j < _len1; _j++) {
            el = $elements[_j];
            editor = FrontEndEditor.make_editable(el);
            editor.part_of_group = true;
            _results.push(editor);
          }
          return _results;
        })();
        fieldType = $container.hasClass('status-auto-draft') ? 'createPost' : 'group';
        editor = new FrontEndEditor.fieldTypes[fieldType]($container, editors);
        editor.init_hover($container);
        $container.data('fee-editor', editor);
      }
      _ref1 = jQuery('.fee-field').not('.fee-initialized');
      _results = [];
      for (_j = 0, _len1 = _ref1.length; _j < _len1; _j++) {
        el = _ref1[_j];
        _results.push(FrontEndEditor.make_editable(el, true));
      }
      return _results;
    },
    make_editable: function(el, single) {
      var $el, data, editor, fieldType;
      $el = jQuery(el);
      data = extract_data_attr(el);
      $el.addClass('fee-initialized');
      fieldType = FrontEndEditor.fieldTypes[data.type];
      if (!fieldType) {
        if (console) {
          console.warn('invalid field type', el);
        }
        return;
      }
      editor = new fieldType;
      editor.el = $el;
      editor.data = data;
      if (single) {
        editor.init_hover($el);
        $el.data('fee-editor', editor);
      }
      return editor;
    }
  });

  HOVER_BORDER = 2;

  HORIZONTAL_PADDING = 4;

  VERTICAL_PADDING = 2;

  FrontEndEditor.controls = (function() {
    function controls(container) {
      this.container = container;
      false;
    }

    controls.prototype.not_editing = function($content) {
      return this.container.html($content);
    };

    controls.prototype.editing = function($content, vert_pos) {
      return this.container.html($content);
    };

    return controls;

  })();

  FrontEndEditor.hover = (function() {
    hover.prototype.lock = false;

    hover.prototype.timeout = null;

    function hover(target) {
      var _this = this;
      this.target = target;
      if (this.target.width() > this.target.parent().width()) {
        this.target.css('display', 'block');
      }
      this.border = jQuery('<div>', {
        'class': 'fee-hover-border',
        'css': {
          width: HOVER_BORDER
        }
      }).hide().appendTo('body');
      this.container = jQuery('<div>', {
        'class': 'fee-hover-container'
      }).hide().appendTo('body');
      this.container.click(function(ev) {
        ev.preventDefault();
        return _this.hide_immediately();
      });
      this.target.mousemove(function(ev) {
        return _this.position_vert(ev.pageY);
      });
      this.target.mouseover(function(ev) {
        return _this.show(ev.pageY);
      });
    }

    hover.prototype.not_editing = function($content) {
      var _this = this;
      this.container.html($content);
      this.container.bind('mouseover.autohide', function() {
        return _this.lock = true;
      });
      this.container.bind('mouseout.autohide', function() {
        _this.lock = false;
        return _this.hide();
      });
      return this.target.bind('mouseout.autohide', function(ev) {
        return _this.hide();
      });
    };

    hover.prototype.editing = function($content, vert_pos) {
      this.container.html($content);
      this.target.unbind('.autohide');
      this.container.unbind('.autohide');
      return this.show(vert_pos);
    };

    hover.prototype.hide_immediately = function() {
      this.container.hide();
      return this.border.hide();
    };

    hover.prototype.hide = function() {
      var _this = this;
      return this.timeout = setTimeout(function() {
        if (_this.lock) {
          return;
        }
        return _this.hide_immediately();
      }, 300);
    };

    hover.prototype.position_vert = function(vert_pos) {
      var normal_height;
      if (vert_pos != null) {
        normal_height = vert_pos - this.container.outerHeight() / 2 - HOVER_BORDER;
      } else {
        normal_height = this.target.offset().top - HOVER_BORDER * 2;
      }
      return this.container.css('top', normal_height + 'px');
    };

    hover.prototype.show = function(vert_pos) {
      var offset;
      this.position_vert(vert_pos);
      offset = this.target.offset();
      clearTimeout(this.timeout);
      this.container.css('left', (offset.left - this.container.outerWidth() - HORIZONTAL_PADDING - HOVER_BORDER) + 'px');
      this.container.show();
      return this.border.css({
        'left': (offset.left - HORIZONTAL_PADDING - HOVER_BORDER) + 'px',
        'top': (offset.top - VERTICAL_PADDING - HOVER_BORDER) + 'px',
        'height': (this.target.outerHeight() + VERTICAL_PADDING * 2) + 'px'
      }).show();
    };

    return hover;

  })();

  jQuery(document).ready(function() {
    var $el, $widget, el, _i, _len, _ref;
    _ref = jQuery('[data-filter="widget_title"], [data-filter="widget_text"]');
    for (_i = 0, _len = _ref.length; _i < _len; _i++) {
      el = _ref[_i];
      $el = jQuery(el);
      $widget = $el.closest('.widget_text');
      if ($widget.length) {
        $el.attr('data-widget_id', $widget.attr('id'));
        $widget.addClass('fee-group');
      } else {
        $el.unwrap();
      }
    }
    return FrontEndEditor.init_fields();
  });

  jQuery(window).load(function() {
    var _ref;
    return (_ref = jQuery('.fee-group.status-auto-draft').data('fee-editor')) != null ? _ref.start_editing() : void 0;
  });

  FrontEndEditor.fieldTypes.base = (function() {
    function base() {}

    base.prototype.el = null;

    base.prototype.get_type = function() {
      return this.constructor.name;
    };

    base.prototype.pre_edit_button = function() {
      var _this = this;
      return jQuery('<button>', {
        'class': 'fee-hover-edit',
        'html': FrontEndEditor.data.edit_text,
        'click': function(ev) {
          _this.last_mouse_pos = ev.pageY;
          return _this.start_editing();
        }
      });
    };

    base.prototype.start_editing = null;

    base.prototype.init_hover = function($target) {
      this.hover = new FrontEndEditor.hover($target);
      return this.hover.not_editing(this.pre_edit_button());
    };

    base.prototype.ajax_get = function() {
      var _this = this;
      this.el.trigger('edit_start');
      return this._ajax_request({
        data: this.ajax_get_args.apply(this, arguments),
        success: function() {
          _this.ajax_get_handler.apply(_this, arguments);
          return _this.el.trigger('edit_started');
        }
      });
    };

    base.prototype.ajax_set = function() {
      var _this = this;
      this.el.trigger('edit_save');
      return this._ajax_request({
        data: this.ajax_set_args.apply(this, arguments),
        success: function() {
          _this.ajax_set_handler.apply(_this, arguments);
          return _this.el.trigger('edit_saved');
        }
      });
    };

    base.prototype._ajax_request = function(args) {
      args.url = FrontEndEditor.data.ajax_url;
      args.type = 'POST';
      args.dataType = 'json';
      return jQuery.ajax(args);
    };

    base.prototype.ajax_get_handler = null;

    base.prototype.ajax_set_handler = null;

    base.prototype.ajax_get_args = function() {
      var args;
      args = this.ajax_args();
      args.callback = 'get';
      return args;
    };

    base.prototype.ajax_set_args = function(content) {
      var args;
      args = this.ajax_args();
      args.callback = 'save';
      args.content = content;
      return args;
    };

    base.prototype.ajax_args = function() {
      return {
        action: 'front-end-editor',
        nonce: FrontEndEditor.data.nonce,
        data: this.data
      };
    };

    return base;

  })();

  FrontEndEditor.fieldTypes.input = (function(_super) {
    __extends(input, _super);

    function input() {
      _ref = input.__super__.constructor.apply(this, arguments);
      return _ref;
    }

    input.prototype.input_tag = '<input type="text">';

    input.prototype.start_editing = function() {
      this.create_form();
      this.create_input();
      return this.ajax_get();
    };

    input.prototype.create_buttons = function() {
      var _this = this;
      this.save_button = jQuery('<button>', {
        'class': 'fee-form-save',
        'text': FrontEndEditor.data.save_text,
        'click': function() {
          return _this.ajax_set();
        }
      });
      this.cancel_button = jQuery('<button>', {
        'class': 'fee-form-cancel',
        'text': FrontEndEditor.data.cancel_text,
        'click': function() {
          return _this.remove_form();
        }
      });
      return this.save_button.add(this.cancel_button);
    };

    input.prototype.create_form = function() {
      var _this = this;
      this.form = jQuery(this.el.is('span') ? '<span>' : '<div>').addClass('fee-form').addClass('fee-type-' + this.get_type());
      return this.form.keypress(function(ev) {
        return _this.keypress(ev.keyCode || ev.which || ev.charCode || 0);
      });
    };

    input.prototype.remove_form = function() {
      this.form.remove();
      return this.el.show();
    };

    input.prototype.keypress = function(code) {
      var keys;
      keys = {
        ENTER: 13,
        ESCAPE: 27
      };
      if (code === keys.ENTER && 'input' === this.get_type()) {
        this.save_button.click();
      }
      if (code === keys.ESCAPE) {
        return this.cancel_button.click();
      }
    };

    input.prototype.create_input = function() {
      this.input = jQuery(this.input_tag).attr({
        'id': 'fee-' + new Date().getTime(),
        'class': 'fee-form-content'
      });
      return this.input.prependTo(this.form);
    };

    input.prototype.content_to_input = function(content) {
      return this.input.val(content);
    };

    input.prototype.content_from_input = function() {
      return this.input.val();
    };

    input.prototype.content_to_front = function(content) {
      return this.el.html(content);
    };

    input.prototype.ajax_get = function() {
      FrontEndEditor.overlay.cover(this.el);
      return input.__super__.ajax_get.apply(this, arguments);
    };

    input.prototype.ajax_set_args = function(contentData) {
      FrontEndEditor.overlay.cover(this.form);
      if (0 === arguments.length) {
        contentData = this.content_from_input();
      }
      return input.__super__.ajax_set_args.call(this, contentData);
    };

    input.prototype.ajax_get_handler = function(response) {
      var $el;
      $el = this.error_handler(response);
      if (!$el) {
        return;
      }
      this.el.hide();
      this.form.insertAfter($el);
      this.content_to_input(response.content);
      this.input.focus();
      if (!this.part_of_group) {
        return this.show_control_buttons();
      }
    };

    input.prototype.show_control_buttons = function() {
      var hover;
      hover = new FrontEndEditor.hover(this.form);
      return hover.editing(this.create_buttons(), this.last_mouse_pos);
    };

    input.prototype.ajax_set_handler = function(response) {
      var $el;
      $el = this.error_handler(response);
      if (!$el) {
        return;
      }
      this.content_to_front(response.content);
      return this.remove_form();
    };

    input.prototype.error_handler = function(response) {
      var $el, $parent;
      $parent = this.el.closest('a');
      $el = $parent.length ? $parent : this.el;
      FrontEndEditor.overlay.hide();
      if (response.error) {
        jQuery('<div class="fee-error">').append(jQuery('<span class="fee-message">').html(response.error)).append(jQuery('<span class="fee-dismiss">x</span>').click(function() {
          return $error_box.remove();
        })).insertBefore($el);
        return false;
      }
      return $el;
    };

    return input;

  })(FrontEndEditor.fieldTypes.base);

  FrontEndEditor.fieldTypes.select = (function(_super) {
    __extends(select, _super);

    function select() {
      _ref1 = select.__super__.constructor.apply(this, arguments);
      return _ref1;
    }

    select.prototype.input_tag = '<select>';

    select.prototype.content_to_input = function(content) {
      var title, value, _ref2;
      _ref2 = this.data.values;
      for (value in _ref2) {
        if (!__hasProp.call(_ref2, value)) continue;
        title = _ref2[value];
        this.input.append(jQuery('<option>', {
          value: value,
          html: title,
          selected: content === value
        }));
      }
      return false;
    };

    select.prototype.content_from_input = function() {
      return this.input.find(':selected').val();
    };

    return select;

  })(FrontEndEditor.fieldTypes.input);

  FrontEndEditor.fieldTypes.textarea = (function(_super) {
    __extends(textarea, _super);

    function textarea() {
      _ref2 = textarea.__super__.constructor.apply(this, arguments);
      return _ref2;
    }

    textarea.prototype.input_tag = '<textarea rows="10">';

    return textarea;

  })(FrontEndEditor.fieldTypes.input);

  FrontEndEditor.fieldTypes.image_base = (function(_super) {
    var _ref4;

    __extends(image_base, _super);

    function image_base() {
      _ref3 = image_base.__super__.constructor.apply(this, arguments);
      return _ref3;
    }

    image_base.prototype.button_text = (_ref4 = FrontEndEditor.data.image) != null ? _ref4.change : void 0;

    image_base.prototype.start_editing = function() {
      var _this = this;
      tb_show(this.button_text, FrontEndEditor.data.image.url);
      jQuery('#TB_closeWindowButton img').attr('src', FrontEndEditor.data.image.tb_close);
      return jQuery('#TB_iframeContent').load(function(ev) {
        var $thickbox, iframe;
        iframe = ev.currentTarget.contentWindow;
        $thickbox = iframe.jQuery(iframe.document);
        _this.thickbox_load($thickbox);
        if (jQuery.noop !== _this.media_item_manipulation) {
          $thickbox.find('.media-item').each(function(i, el) {
            return _this.media_item_manipulation(iframe.jQuery(el));
          });
          return $thickbox.ajaxComplete(function(event, request) {
            var item_id;
            item_id = jQuery(request.responseText).find('.media-item-info').attr('id');
            return _this.media_item_manipulation($thickbox.find('#' + item_id).closest('.media-item'));
          });
        }
      });
    };

    image_base.prototype.thickbox_load = function($thickbox) {
      var _this = this;
      return $thickbox.delegate('.media-item :submit', 'click', function(ev) {
        var $button, data;
        $button = jQuery(ev.currentTarget);
        data = $button.closest('form').serializeArray();
        data.push({
          name: $button.attr('name'),
          value: $button.attr('name')
        });
        data.push({
          name: 'action',
          value: 'fee_image_insert'
        });
        jQuery.post(FrontEndEditor.data.ajax_url, data, function(html) {
          return _this.image_html_handler(html);
        });
        return false;
      });
    };

    image_base.prototype.media_item_manipulation = function($item) {
      $item.find('#go_button').remove();
      return $item.find(':submit').val(this.button_text);
    };

    return image_base;

  })(FrontEndEditor.fieldTypes.base);

  FrontEndEditor.fieldTypes.image = (function(_super) {
    __extends(image, _super);

    function image() {
      _ref4 = image.__super__.constructor.apply(this, arguments);
      return _ref4;
    }

    image.prototype.start_editing = function() {
      var _this = this;
      image.__super__.start_editing.apply(this, arguments);
      return jQuery('<a id="fee-img-revert" href="#">').text(FrontEndEditor.data.image.revert).click(function(ev) {
        _this.ajax_set(-1);
        return false;
      }).insertAfter('#TB_ajaxWindowTitle');
    };

    image.prototype.media_item_manipulation = function($item) {
      $item.find('tbody tr').not('.image-size, .submit').hide();
      return image.__super__.media_item_manipulation.apply(this, arguments);
    };

    image.prototype.image_html_handler = function(html) {
      var $html;
      $html = jQuery(html);
      if ($html.is('a')) {
        $html = $html.find('img');
      }
      return this.ajax_set($html.attr('src'));
    };

    image.prototype.ajax_set_handler = function(response) {
      var url;
      url = response.content;
      if ('-1' === url) {
        return location.reload(true);
      } else {
        this.el.find('img').attr('src', url);
        return tb_remove();
      }
    };

    return image;

  })(FrontEndEditor.fieldTypes.image_base);

  FrontEndEditor.fieldTypes.thumbnail = (function(_super) {
    __extends(thumbnail, _super);

    function thumbnail() {
      _ref5 = thumbnail.__super__.constructor.apply(this, arguments);
      return _ref5;
    }

    thumbnail.prototype.thickbox_load = function($thickbox) {
      var _this = this;
      $thickbox.find('#tab-type_url').remove();
      return $thickbox.delegate('.media-item :submit', 'click', function(ev) {
        var $item, attachment_id;
        $item = jQuery(ev.currentTarget).closest('.media-item').find('.media-item-info');
        attachment_id = $item.attr('id').replace('media-head-', '');
        _this.ajax_set(attachment_id);
        return false;
      });
    };

    thumbnail.prototype.media_item_manipulation = function($item) {
      $item.find('tbody tr').not('.submit').remove();
      return thumbnail.__super__.media_item_manipulation.apply(this, arguments);
    };

    return thumbnail;

  })(FrontEndEditor.fieldTypes.image);

  if (typeof Aloha !== "undefined" && Aloha !== null) {
    Aloha.require(['aloha/selection'], function(Selection) {
      var _ref6;
      return FrontEndEditor.fieldTypes.image_rich = (function(_super) {
        var _ref7;

        __extends(image_rich, _super);

        function image_rich() {
          _ref6 = image_rich.__super__.constructor.apply(this, arguments);
          return _ref6;
        }

        image_rich.prototype.button_text = (_ref7 = FrontEndEditor.data.image) != null ? _ref7.insert : void 0;

        image_rich.prototype.start_editing = function() {
          jQuery('.aloha-floatingmenu, #aloha-floatingmenu-shadow').hide();
          return image_rich.__super__.start_editing.apply(this, arguments);
        };

        image_rich.prototype.media_item_manipulation = jQuery.noop;

        image_rich.prototype.image_html_handler = function(html) {
          GENTICS.Utils.Dom.insertIntoDOM(jQuery(html), Selection.getRangeObject(), Aloha.activeEditable.obj);
          tb_remove();
          return jQuery('.aloha-floatingmenu, #aloha-floatingmenu-shadow').show();
        };

        return image_rich;

      })(FrontEndEditor.fieldTypes.image_base);
    });
  }

  FrontEndEditor.fieldTypes.rich = (function(_super) {
    __extends(rich, _super);

    function rich() {
      _ref6 = rich.__super__.constructor.apply(this, arguments);
      return _ref6;
    }

    rich.prototype.content_from_input = function() {
      return Aloha.getEditableById(this.form.attr('id')).getContents();
    };

    rich.prototype.create_input = jQuery.noop;

    rich.prototype.create_form = function() {
      return this.form = Aloha.jQuery('<div class="fee-form fee-type-rich">');
    };

    rich.prototype.remove_form = function() {
      this.form.mahalo();
      return rich.__super__.remove_form.apply(this, arguments);
    };

    rich.prototype.ajax_get_handler = function(response) {
      var $el;
      $el = this.error_handler(response);
      if (!$el) {
        return;
      }
      this.form.html(response.content);
      this.el.hide();
      this.form.insertAfter($el);
      this.form.aloha();
      if (!this.part_of_group) {
        this.show_control_buttons();
        this.form.focus();
        return this.form.dblclick();
      }
    };

    return rich;

  })(FrontEndEditor.fieldTypes.textarea);

  FrontEndEditor.fieldTypes.terminput = (function(_super) {
    __extends(terminput, _super);

    function terminput() {
      _ref7 = terminput.__super__.constructor.apply(this, arguments);
      return _ref7;
    }

    terminput.prototype.content_to_input = function(content) {
      terminput.__super__.content_to_input.apply(this, arguments);
      return this.input.suggest(FrontEndEditor.data.ajax_url + '?action=ajax-tag-search&tax=' + this.data.taxonomy, {
        multiple: true,
        resultsClass: 'fee-suggest-results',
        selectClass: 'fee-suggest-over',
        matchClass: 'fee-suggest-match'
      });
    };

    return terminput;

  })(FrontEndEditor.fieldTypes.input);

  FrontEndEditor.fieldTypes.termselect = (function(_super) {
    __extends(termselect, _super);

    function termselect() {
      _ref8 = termselect.__super__.constructor.apply(this, arguments);
      return _ref8;
    }

    termselect.prototype.content_to_input = function(content) {
      var $dropdown;
      $dropdown = jQuery(content);
      this.input.replaceWith($dropdown);
      return this.input = $dropdown;
    };

    return termselect;

  })(FrontEndEditor.fieldTypes.select);

  FrontEndEditor.fieldTypes.widget = (function(_super) {
    __extends(widget, _super);

    function widget() {
      _ref9 = widget.__super__.constructor.apply(this, arguments);
      return _ref9;
    }

    widget.prototype.create_input = jQuery.noop;

    widget.prototype.content_to_input = function(content) {
      this.input = jQuery(content);
      return this.form.prepend(content);
    };

    widget.prototype.ajax_set_args = function() {
      var args, name, value, _i, _len, _ref10, _ref11;
      args = widget.__super__.ajax_set_args.apply(this, arguments);
      _ref10 = this.form.find(':input').serializeArray();
      for (_i = 0, _len = _ref10.length; _i < _len; _i++) {
        _ref11 = _ref10[_i], name = _ref11.name, value = _ref11.value;
        args[name] = args[name] === void 0 ? value : jQuery.isArray(args[name]) ? args[name].concat(value) : [args[name], value];
      }
      return args;
    };

    return widget;

  })(FrontEndEditor.fieldTypes.textarea);

  FrontEndEditor.fieldTypes.group = (function(_super) {
    __extends(group, _super);

    function group(el, editors) {
      this.el = el;
      this.editors = editors;
      group.__super__.constructor.apply(this, arguments);
    }

    group.prototype.create_input = jQuery.noop;

    group.prototype.init_hover = function($container) {
      var $button_area;
      $button_area = $container.find('.fee-buttons');
      if (!$button_area.length) {
        return group.__super__.init_hover.apply(this, arguments);
      } else {
        this.hover = new FrontEndEditor.controls($button_area);
        return this.hover.not_editing(this.pre_edit_button());
      }
    };

    group.prototype.create_form = function() {
      var editor, _i, _len, _ref10;
      _ref10 = this.editors;
      for (_i = 0, _len = _ref10.length; _i < _len; _i++) {
        editor = _ref10[_i];
        editor.create_form();
        editor.create_input();
      }
      return this.form = this.el;
    };

    group.prototype.remove_form = function() {
      var editor, _i, _len, _ref10;
      _ref10 = this.editors;
      for (_i = 0, _len = _ref10.length; _i < _len; _i++) {
        editor = _ref10[_i];
        editor.remove_form();
      }
      return this.hover.not_editing(this.pre_edit_button());
    };

    group.prototype.content_from_input = function() {
      var editor, _i, _len, _ref10, _results;
      _ref10 = this.editors;
      _results = [];
      for (_i = 0, _len = _ref10.length; _i < _len; _i++) {
        editor = _ref10[_i];
        _results.push(editor.content_from_input());
      }
      return _results;
    };

    group.prototype.keypress = jQuery.noop;

    group.prototype.ajax_set = function() {
      group.__super__.ajax_set.apply(this, arguments);
      return FrontEndEditor.overlay.cover(this.el);
    };

    group.prototype.ajax_args = function() {
      var args, commonData, data, dataArr, editor, i, item, key, value, _i, _ref10;
      args = group.__super__.ajax_args.apply(this, arguments);
      args.group = true;
      dataArr = (function() {
        var _i, _len, _ref10, _results;
        _ref10 = this.editors;
        _results = [];
        for (_i = 0, _len = _ref10.length; _i < _len; _i++) {
          editor = _ref10[_i];
          _results.push(editor.data);
        }
        return _results;
      }).call(this);
      if (dataArr.length === 1) {
        args.data = dataArr;
      } else {
        commonData = jQuery.extend({}, dataArr[0]);
        for (i = _i = 1, _ref10 = dataArr.length; 1 <= _ref10 ? _i < _ref10 : _i > _ref10; i = 1 <= _ref10 ? ++_i : --_i) {
          for (key in commonData) {
            if (!__hasProp.call(commonData, key)) continue;
            value = commonData[key];
            if (value !== dataArr[i][key]) {
              delete commonData[key];
            }
          }
        }
        args.data = (function() {
          var _j, _len, _results;
          _results = [];
          for (_j = 0, _len = dataArr.length; _j < _len; _j++) {
            data = dataArr[_j];
            item = {};
            for (key in data) {
              if (!__hasProp.call(data, key)) continue;
              if (__indexOf.call(commonData, key) < 0) {
                item[key] = data[key];
              }
            }
            _results.push(item);
          }
          return _results;
        })();
        args.commonData = commonData;
      }
      return args;
    };

    group.prototype.ajax_get_handler = function(response) {
      var editor, i, _i, _len, _ref10, _ref11;
      _ref10 = this.editors;
      for (i = _i = 0, _len = _ref10.length; _i < _len; i = ++_i) {
        editor = _ref10[i];
        editor.ajax_get_handler(response[i]);
      }
      if ((_ref11 = this.editors[0].input) != null) {
        _ref11.focus();
      }
      return this.hover.editing(this.create_buttons(), this.last_mouse_pos);
    };

    group.prototype.ajax_set_handler = function(response) {
      var editor, i, _i, _len, _ref10;
      _ref10 = this.editors;
      for (i = _i = 0, _len = _ref10.length; _i < _len; i = ++_i) {
        editor = _ref10[i];
        editor.ajax_set_handler(response[i]);
      }
      return this.remove_form();
    };

    return group;

  })(FrontEndEditor.fieldTypes.input);

  FrontEndEditor.fieldTypes.createPost = (function(_super) {
    __extends(createPost, _super);

    function createPost() {
      _ref10 = createPost.__super__.constructor.apply(this, arguments);
      return _ref10;
    }

    createPost.prototype.ajax_set_args = function() {
      var args;
      args = createPost.__super__.ajax_set_args.apply(this, arguments);
      args.createPost = true;
      return args;
    };

    createPost.prototype.ajax_set_handler = function(response) {
      return window.location = response.permalink;
    };

    return createPost;

  })(FrontEndEditor.fieldTypes.group);

}).call(this);
