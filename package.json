{"name": "nraa", "version": "2.0.0", "description": "The National Rifle Association of Australia web application", "author": "Zenkey Group", "main": "public_html/index.php", "devDependencies": {"@babel/core": "^7.23.0", "@babel/eslint-parser": "^7.23.0", "@babel/preset-env": "^7.23.0", "coffeescript": "^2.7.0", "eslint": "^8.52.0", "fancy-log": "^2.0.0", "gulp": "^4.0.2", "gulp-autoprefixer": "^8.0.0", "gulp-babel": "^8.0.0", "gulp-clean-css": "^4.3.0", "gulp-coffee": "^3.0.3", "gulp-concat": "^2.6.1", "gulp-eslint": "^6.0.0", "gulp-insert": "^0.5.0", "gulp-less": "^5.0.0", "gulp-rename": "^2.0.0", "gulp-replace": "^1.1.4", "gulp-sourcemaps": "^3.0.0", "gulp-terser": "^2.1.0", "less": "^4.2.0", "streamqueue": "^1.1.2"}, "scripts": {"compile": "gulp", "compile-js": "gulp js", "compile-css": "gulp css"}, "repository": {"type": "git", "url": "https://bitbucket.org/zenkey/nraa"}, "babel": {"presets": ["@babel/preset-env"]}, "eslintConfig": {"env": {"browser": true, "jquery": true, "es6": true}, "parser": "@babel/eslint-parser", "parserOptions": {"ecmaVersion": 2022, "sourceType": "module", "requireConfigFile": false, "ecmaFeatures": {"impliedStrict": true}}}, "dependencies": {"gulp-cli": "^2.1.0"}}