#!/usr/bin/env bash
set -e

echo "Checking for required database files..."

if [[ ! -f db/nraa_wp.sql.gz ]] || [[ ! -f db/nraa_data.sql.gz ]]; then
  echo "Error: Required database files not found!"
  echo "Expected files:"
  echo "  - db/nraa_wp.sql.gz"
  echo "  - db/nraa_data.sql.gz"
  echo ""
  echo "Please ensure these files are in the db/ directory before running this script."
  exit 1
fi

echo "Database files found. Setting up databases..."

# Wait for MySQL to be ready
echo "Waiting for MySQL to be ready..."
until docker compose exec database mysqladmin ping -h"localhost" --silent; do
    echo "Waiting for database connection..."
    sleep 2
done

echo "Creating databases..."
docker compose exec database sh -c "cat /opt/db/create_databases.sql | mysql -u root -ppassword"

echo "Loading WordPress database..."
docker compose exec database sh -c "zcat /opt/db/nraa_wp.sql.gz | mysql -u root -ppassword nraa_wp"

echo "Loading NRAA data database..."
docker compose exec database sh -c "zcat /opt/db/nraa_data.sql.gz | mysql -u root -ppassword nraa_data"

echo "Database setup complete!"
