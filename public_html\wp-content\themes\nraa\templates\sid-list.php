<?php
/**
 * Shooter Template
 */
$disciplines = Database::factory('Discipline')
    ->order_by('id', 'ASC')
    ->limit(4)
    ->find_all();
$pagination = new Pagination(
    Database::factory('Shooter')
        ->join('ShooterDetails')
        ->on('ShooterDetails', 'shooter_details.shooter_id', '=', 'shooters.id'),
    get_permalink()
);
$pagination->page_key = '_p';
$pagination->search_fields = array(
    'sid', 'club', 'email', 'preferred_name', 'first_name', 'last_name'
);
$shooters = $pagination->getData();
?>

<div class="shooter-list">
    <form method="get" class="search-form">
        <?php if (isset($_GET['search'])): ?>
            <a href="<?php the_permalink(); ?>"><?php _e('Clear'); ?></a>
        <?php endif; ?>
        <input type="text" name="search" value="<?php echo isset($_GET['search']) ? $_GET['search'] : ''; ?>" autocomplete="off" class="search-bar">
        <input type="submit" value="search">
    </form>
    <div class="clear"></div>

    <table class="data-table">
        <thead>
        <tr>
            <th class="icon"><span class="ui-icon ui-collapsible-icon ui-collapsible-icon-d"></span></th>
            <th><?php _e('SID'); ?></th>
            <th><?php _e('Last Name'); ?></th>
            <th><?php _e('First Name'); ?></th>
            <th><?php _e('Pref Name'); ?></th>
            <th><?php _e('Club'); ?></th>
        </tr>
        </thead>
        <tbody>
        <?php
        $i = 1;
        foreach ($shooters as $s): ?>
            <tr class="goToGrade tooltip" data-shooter-id="<?php echo $s->id; ?>" title="Click to view results">
                <td class="center"><?php echo $i++; ?></td>
                <td><?php echo $s->sid; ?></td>
                <td><?php echo $s->last_name; ?></td>
                <td><?php echo $s->first_name; ?></td>
                <td><?php echo $s->preferred_name; ?></td>
                <td><?php echo $s->club; ?></td>
            </tr>
            <tr class="hiddenData">
                <td colspan="6">
                    <table width="100%">
                        <tr>
                            <th width="50%"><?php _e('Discipline'); ?></th>
                            <th><?php _e('Average Score %'); ?></th>
                            <th><?php _e('Number of Shoots'); ?></th>
                            <th><?php _e('Grade'); ?></th>
                        </tr>
                        <?php foreach ($disciplines as $discipline): ?>
                        <tr class="displayResults" data-discipline-id="<?php echo $discipline->id; ?>" data-shooter-id="<?php echo $s->id; ?>">
                            <td><?php echo $discipline->name; ?></td>
                            <td colspan="3"><?php _e('No grading data'); ?></td>
                        </tr>
                        <?php endforeach; ?>
                    </table>
                </td>
            </tr>
        <?php endforeach; ?>
        </tbody>
    </table>
    <?php echo $pagination; ?>
</div>

<div id="results-modal" title="Results">
  <table class="data-table">
    <thead>
    <tr>
      <th width="5%"><?php _e('Graded'); ?></th>
      <th><?php _e('Competition'); ?></th>
      <th><?php _e('Match #'); ?></th>
      <th><?php _e('High Score'); ?></th>
      <th><?php _e('Score'); ?></th>
      <th><?php _e('Score %'); ?></th>
    </tr>
    </thead>
    <tbody>
    </tbody>
  </table>
</div>
