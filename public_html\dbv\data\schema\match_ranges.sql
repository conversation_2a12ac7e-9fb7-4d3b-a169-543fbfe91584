CREATE TABLE `match_ranges` (
  `match_id` int(11) unsigned NOT NULL,
  `range_id` int(10) unsigned NOT NULL,
  UNIQUE KEY `unique_combination` (`match_id`,`range_id`),
  KEY `range_id` (`range_id`),
  CONSTRAINT `match_ranges_ibfk_3` FOREIGN KEY (`match_id`) REFERENCES `matches` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
  CONSTRAINT `match_ranges_ibfk_4` FOREIGN KEY (`range_id`) REFERENCES `ranges` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=latin1