/**
 * Task Automation to make life easier.
 *
 * Author: <PERSON> <<EMAIL>>
 * Updated: 24/05/2016.
 */

// declarations, dependencies
// -----------------------------------------------------------------------------
import gulp from 'gulp';
import less from 'gulp-less';
import log from 'fancy-log';
import babel from 'gulp-babel';
import terser from 'gulp-terser';
import concat from 'gulp-concat';
import coffee from 'gulp-coffee';
import eslint from 'gulp-eslint';
import insert from 'gulp-insert';
import rename from 'gulp-rename';
import replace from 'gulp-replace';
import cleanCss from 'gulp-clean-css';
import streamqueue from 'streamqueue';
import sourcemaps from 'gulp-sourcemaps';
import autoprefixer from 'gulp-autoprefixer';

const baseDir = './public_html/wp-content/themes/nraa';
let version = null;

// gulp tasks
// -----------------------------------------------------------------------------
function lint() {
  return gulp.src('./assets/js/**/*.js')
    .on('error', log)
    .pipe(eslint())
    .pipe(eslint.format());
}

function updateVersion() {
  return gulp.src(`${baseDir}/functions.php`)
    .pipe(replace(
      /define\(\s*'VERSION',\s*([0-9\.]+)\)/,
      (match, oldVersion, offset, string) => {
        version = (parseFloat(oldVersion) + 0.001).toFixed(3);
        return `define('VERSION', ${version})`;
      }))
    .pipe(gulp.dest(baseDir));
}

function coffeeTask() {
  return gulp.src(`${baseDir}/js/*.coffee`)
    .pipe(rename({ extname: '.min.js' }))
    .pipe(sourcemaps.init())
    .pipe(coffee().on('error', log))
    .pipe(terser())
    .pipe(sourcemaps.write('.'))
    .pipe(gulp.dest(`${baseDir}/js/dist/`));
}

function scripts() {
  return gulp.src([`!${baseDir}/js/*.min.js`, `${baseDir}/js/*.js`])
    .on('error', log)
    .pipe(rename({ extname: '.min.js' }))
    .pipe(sourcemaps.init())
    .pipe(babel())
    .pipe(terser())
    .pipe(sourcemaps.write('.'))
    .pipe(gulp.dest(`${baseDir}/js/dist/`));
}

/* style.css: Theme styles */
function style() {
  return streamqueue(
    { objectMode: true },
    gulp.src(`${baseDir}/css/style/*.less`)
      .pipe(less()),
    gulp.src(`${baseDir}/css/style/*.css`)
  )
    .on('error', log)
    .pipe(sourcemaps.init())
    .pipe(autoprefixer())
    .pipe(concat('style.css'))
    .pipe(cleanCss())
    .pipe(sourcemaps.write('.'))
    .pipe(insert.transform((contents, file) => {
      if (file.path.endsWith('.map'))
        return contents;

      return `/*
Theme Name: NRAA
Theme URI: http://www.nraa.com.au/
Description: The redesign of the NRAA website
Author: Zenkey Group Pty Ltd
Author URI: http://www.zenkey.com.au/
Version: ${version}
*/
${contents}`;
    }))
    .pipe(gulp.dest(`${baseDir}/`));
}

/* Less styles */
function lessTask() {
  return gulp.src(`${baseDir}/css/*.less`)
    .on('error', log)
    .pipe(sourcemaps.init())
    .pipe(less())
    .pipe(autoprefixer())
    .pipe(cleanCss())
    .pipe(rename({ extname: '.min.css' }))
    .pipe(sourcemaps.write('.'))
    .pipe(gulp.dest(`${baseDir}/css/dist/`));
}

/* All other styles */
function otherStyles() {
  return gulp.src([`!${baseDir}/css/*.min.css`, `${baseDir}/css/*.css`])
    .on('error', log)
    .pipe(sourcemaps.init())
    .pipe(autoprefixer())
    .pipe(cleanCss())
    .pipe(rename({ extname: '.min.css' }))
    .pipe(sourcemaps.write('.'))
    .pipe(gulp.dest(`${baseDir}/css/dist/`));
}

function watchCss() {
  return gulp.watch([
    `!${baseDir}/css/*.min.css`,
    `${baseDir}/css/*.css`,
    `${baseDir}/css/*.less`,
    `${baseDir}/css/style/*.css`,
    `${baseDir}/css/style/*.less`,
  ], css);
}

// Task compositions
const js = gulp.series(updateVersion, gulp.parallel(lint, coffeeTask, scripts));
const css = gulp.series(updateVersion, gulp.parallel(style, otherStyles, lessTask));

// When running 'gulp' on the terminal this task will fire.
// It will start watching for changes in every .js files.
// If there's a change, the task 'scripts' defined above will fire.
const defaultTask = gulp.series(gulp.parallel(js, css), watchCss);

// Export tasks
export { lint, updateVersion, coffeeTask, scripts, style, lessTask, otherStyles, watchCss, js, css };
export default defaultTask;
