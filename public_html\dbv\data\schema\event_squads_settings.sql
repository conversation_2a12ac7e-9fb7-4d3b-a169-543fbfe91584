CREATE TABLE `event_squads_settings` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `event_id` int(10) unsigned NOT NULL,
  `subevent_id` int(10) unsigned DEFAULT NULL,
  `start_target` int(11) NOT NULL DEFAULT '1',
  `end_target` int(11) NOT NULL DEFAULT '30',
  `is_group_left` tinyint(1) NOT NULL DEFAULT '0',
  `is_group_fclass` tinyint(1) NOT NULL DEFAULT '0',
  `allocate_by` enum('entry','discipline') NOT NULL DEFAULT 'entry',
  PRIMARY KEY (`id`),
  UNIQUE KEY `event_id_subevent_id` (`event_id`,`subevent_id`),
  KEY `event_id` (`event_id`),
  KEY `subevent_id` (`subevent_id`),
  CONSTRAINT `event_squads_settings_ibfk_1` FOREIGN KEY (`event_id`) REFERENCES `events` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
  CONSTRAINT `event_squads_settings_ibfk_2` FOREIGN KEY (`subevent_id`) REFERENCES `subevents` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=latin1