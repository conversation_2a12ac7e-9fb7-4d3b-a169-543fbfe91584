ALTER TABLE `events` ADD `extra_data` text NOT NULL DEFAULT '';

CREATE TABLE `event_presentation_orderings` (
  `event_id` int(10) unsigned NOT NULL,
  `grade_id` int(10) unsigned NOT NULL,
  `order` tinyint NOT NULL,
  FOR<PERSON><PERSON><PERSON> KEY (`event_id`) REFERENCES `events` (`id`),
  FOREIGN KEY (`grade_id`) REFERENCES `grades` (`id`)
) ENGINE='InnoDB';

ALTER TABLE `event_presentation_orderings`
ADD UNIQUE `event_id_grade_id` (`event_id`, `grade_id`),
ADD UNIQUE `event_id_order` (`event_id`, `order`);
