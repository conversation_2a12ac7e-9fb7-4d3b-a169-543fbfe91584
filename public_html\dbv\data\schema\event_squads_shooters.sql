CREATE TABLE `event_squads_shooters` (
  `event_squad_id` int(10) unsigned NOT NULL,
  `shooter_id` int(10) unsigned NOT NULL,
  UNIQUE KEY `event_shooter` (`event_squad_id`,`shooter_id`),
  <PERSON><PERSON>Y `event_squad` (`event_squad_id`),
  <PERSON><PERSON><PERSON> `shooter` (`shooter_id`),
  CONSTRAINT `event_squads_shooters_ibfk_1` FOREIGN KEY (`event_squad_id`) REFERENCES `event_squads` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
  CONSTRAINT `event_squads_shooters_ibfk_2` FOREIGN KEY (`shooter_id`) REFERENCES `shooters` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=latin1