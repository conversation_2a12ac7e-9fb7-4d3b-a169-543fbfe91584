define({
	"size.natural": "Originalgröße",
	"button.addimg.tooltip": "Bild hinzufügen",
	"floatingmenu.tab.img": "Bild",
	"floatingmenu.tab.formatting": "Formatierung",
	"floatingmenu.tab.resize": "Gr<PERSON>ße anpassen",
	"floatingmenu.tab.crop": "Zuschneiden",
	"button.uploadimg.tooltip": "Bild hochladen",
	"button.uploadimg.label": "Hochladen",
	"button.img.align.left.tooltip": "Linksbündig",
	"button.img.align.right.tooltip": "Rechtsbündig",
	"button.img.align.none.tooltip": "Keine Ausrichtung",
	"field.img.title.label": "Titel",
	"field.img.title.tooltip": "Titel",
	"field.img.label": "URL",
	"field.img.tooltip": "Quelle",
	"border ": "<PERSON>hmen hinzufügen",
	"padding.increase ": "Abstand vergrößern",
	"padding.decrease ": "Abstand verkleinern",
	"size.increase ": "Bild vergrößern",
	"size.decrease ": "Bild verkleinern",
	"Resize": "Größe anpassen",
	"Crop": "Zuschneiden",
	"Reset": "Zurücksetzen",
	"Accept": "Bestätigen",
	"Cancel": "Abbrechen",
	"height": "Höhe",
	"width": "Breite",
	"button.toggle.tooltip": "Seitenverhältnis beibehalten",
	"field.img.src.label": "Quelle",
	"field.img.src.tooltip": "Quelle"
});
