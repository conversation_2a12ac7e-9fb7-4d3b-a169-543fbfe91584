CREATE TABLE `shooter_notes` (
  `shooter_id` int(10) unsigned NOT NULL,
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `details` varchar(255) NOT NULL,
  `by` varchar(100) DEFAULT NULL,
  `date` int(11) DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `shooter_id` (`shooter_id`),
  CONSTRAINT `shooter_notes_ibfk_1` FOREIGN KEY (`shooter_id`) REFERENCES `shooters` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=latin1