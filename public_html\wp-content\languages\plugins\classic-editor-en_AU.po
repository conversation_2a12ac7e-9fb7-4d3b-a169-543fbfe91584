# Translation of Plugins - Classic Editor - Stable (latest release) in English (Australia)
# This file is distributed under the same license as the Plugins - Classic Editor - Stable (latest release) package.
msgid ""
msgstr ""
"PO-Revision-Date: 2019-02-02 15:00:11+0000\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=n != 1;\n"
"X-Generator: GlotPress/2.4.0-alpha\n"
"Language: en_AU\n"
"Project-Id-Version: Plugins - Classic Editor - Stable (latest release)\n"

#: classic-editor.php:828
msgctxt "Editor Name"
msgid "Edit (Classic Editor)"
msgstr "Edit (Classic Editor)"

#: classic-editor.php:821
msgctxt "Editor Name"
msgid "Edit (Block Editor)"
msgstr "Edit (Block Editor)"

#: classic-editor.php:457
msgid "Change settings"
msgstr "Change settings"

#: classic-editor.php:444
msgid "Default editor for all sites"
msgstr "Default editor for all sites"

#: classic-editor.php:440
msgid "Editor Settings"
msgstr "Editor Settings"

#: classic-editor.php:424
msgid "Default Editor"
msgstr "Default Editor"

#: classic-editor.php:375 classic-editor.php:452 classic-editor.php:858
#: classic-editor.php:869
msgctxt "Editor Name"
msgid "Block Editor"
msgstr "Block Editor"

#: classic-editor.php:371 classic-editor.php:448 classic-editor.php:855
#: classic-editor.php:869
msgctxt "Editor Name"
msgid "Classic Editor"
msgstr "Classic Editor"

#. translators: %s: post title
#: classic-editor.php:823
msgid "Edit &#8220;%s&#8221; in the Block Editor"
msgstr "Edit &#8220;%s&#8221; in the Block Editor"

#: classic-editor.php:663
msgid "Switch to Block Editor"
msgstr "Switch to Block Editor"

#: classic-editor.php:687
msgid "Switch to Classic Editor"
msgstr "Switch to Classic Editor"

#: classic-editor.php:513
msgid "Change the %1$sClassic Editor settings%2$s."
msgstr "Change the %1$sClassic Editor settings%2$s."

#: classic-editor.php:504
msgid "The Classic Editor plugin prevents use of the new Block Editor."
msgstr "The Classic Editor plugin prevents use of the new Block Editor."

#: classic-editor.php:461
msgid "By default the Block Editor is replaced with the Classic Editor and users cannot switch editors."
msgstr "By default the Block Editor is replaced with the Classic Editor and users cannot switch editors."

#: classic-editor.php:460
msgid "Allow site admins to change settings"
msgstr "Allow site admins to change settings"

#: classic-editor.php:644
msgid "Editor"
msgstr "Editor"

#: classic-editor.php:399
msgid "No"
msgstr "No"

#: classic-editor.php:395
msgid "Yes"
msgstr "Yes"

#: classic-editor.php:322
msgid "Allow users to switch editors"
msgstr "Allow users to switch editors"

#: classic-editor.php:321
msgid "Default editor for all users"
msgstr "Default editor for all users"

#. Author URI of the plugin
msgid "https://github.com/WordPress/classic-editor/"
msgstr "https://github.com/WordPress/classic-editor/"

#. Plugin URI of the plugin
msgid "https://wordpress.org/plugins/classic-editor/"
msgstr "https://en-au.wordpress.org/plugins/classic-editor/"

#. Author of the plugin
msgid "WordPress Contributors"
msgstr "WordPress Contributors"

#. Description of the plugin
msgid "Enables the WordPress classic editor and the old-style Edit Post screen with TinyMCE, Meta Boxes, etc. Supports the older plugins that extend this screen."
msgstr "Enables the WordPress classic editor and the old-style Edit Post screen with TinyMCE, Meta Boxes, etc. Supports the older plugins that extend this screen."

#. Plugin Name of the plugin
msgid "Classic Editor"
msgstr "Classic Editor"

#. translators: %s: post title
#: classic-editor.php:830
msgid "Edit &#8220;%s&#8221; in the Classic Editor"
msgstr "Edit &#8220;%s&#8221; in the Classic Editor"

#: classic-editor.php:706
msgid "Settings"
msgstr "Settings"