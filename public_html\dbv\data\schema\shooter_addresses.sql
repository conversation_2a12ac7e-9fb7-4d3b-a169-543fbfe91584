CREATE TABLE `shooter_addresses` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `shooter_id` int(10) unsigned NOT NULL,
  `type` enum('primary','mailing') NOT NULL DEFAULT 'primary',
  `line_1` varchar(255) NOT NULL,
  `line_2` varchar(255) DEFAULT NULL,
  `line_3` varchar(255) DEFAULT NULL,
  `suburb` varchar(255) NOT NULL,
  `state` enum('ACT','NSW','VIC','QLD','SA','WA','TAS','NT') NOT NULL,
  `postcode` varchar(255) NOT NULL,
  `country` varchar(255) NOT NULL,
  PRIMARY KEY (`id`),
  KEY `shooter_id` (`shooter_id`),
  CONSTRAINT `shooter_addresses_ibfk_1` FOREIGN KEY (`shooter_id`) REFERENCES `shooters` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=latin1