CREATE TABLE `shooter_details` (
  `shooter_id` int(10) unsigned NOT NULL,
  `email` varchar(255) NOT NULL,
  `title` varchar(255) NOT NULL,
  `first_name` varchar(255) NOT NULL,
  `middle_name` varchar(255) DEFAULT NULL,
  `last_name` varchar(255) NOT NULL,
  `preferred_name` varchar(255) DEFAULT NULL,
  `gender` enum('male','female') DEFAULT NULL,
  `date_of_birth` int(11) DEFAULT NULL,
  `home_phone` varchar(255) DEFAULT NULL,
  `mobile_phone` varchar(255) DEFAULT NULL,
  `is_right_handed` tinyint(1) NOT NULL DEFAULT '1',
  `is_using_bench` tinyint(1) NOT NULL DEFAULT '0',
  `is_coach` tinyint(1) NOT NULL DEFAULT '0',
  `is_competition_coach` tinyint(1) NOT NULL DEFAULT '0',
  `special_flags` smallint(5) unsigned NOT NULL DEFAULT '0',
  PRIMAR<PERSON> KEY (`shooter_id`),
  CONSTRAINT `shooter_details_ibfk_1` FOREIGN KEY (`shooter_id`) REFERENCES `shooters` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=latin1