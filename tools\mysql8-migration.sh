#!/bin/bash
set -e

echo "Starting MySQL 8.0 compatible NRAA data migration..."

# Set MySQL 8.0 compatibility settings
echo "Setting MySQL 8.0 compatibility settings..."
docker compose exec database mysql -u root -ppassword -e "
SET GLOBAL log_bin_trust_function_creators = 1;
SET GLOBAL sql_mode = 'NO_AUTO_VALUE_ON_ZERO,ERROR_FOR_DIVISION_BY_ZERO,NO_ENGINE_SUBSTITUTION';
"

echo "Loading database structure and data (excluding problematic procedures)..."

# First, load everything except the problematic GradeShooters procedure
docker compose exec database sh -c "
zcat /opt/db/nraa_data.sql.gz | sed '
# Remove the entire problematic GradeShooters procedure
/DROP PROCEDURE IF EXISTS \`GradeShooters\`/,/END;;/d;
' | mysql -u root -ppassword nraa_data
"

echo "Database structure and data loaded successfully."

echo "MySQL 8.0 compatible NRAA data migration completed!"
