upstream php {
    server wordpress:9000;
}

server {
    server_name localhost;
    root        /var/www/html;
    listen      80;

    client_max_body_size 1000M;

    location / {
        index index.php;
        try_files $uri $uri/ /index.php?$args;
    }

    # Add trailing slash to */wp-admin requests
    rewrite /wp-admin$ $scheme://$host$uri/ permanent;

    # Directives to send expires headers and turn off 404 error logging.
    location ~* ^.+\.(ogg|ogv|svg|svgz|eot|otf|woff|mp4|ttf|rss|atom|jpg|jpeg|gif|png|ico|zip|tgz|gz|rar|bz2|doc|xls|exe|ppt|tar|mid|midi|wav|bmp|rtf)$ {
        access_log    off;
        log_not_found off;
        expires       max;
    }

    location ~ [^/]\.php(/|$) {
        fastcgi_split_path_info ^(.+?\.php)(/.*)$;

        if (!-f $document_root$fastcgi_script_name) {
           return 404;
        }

        include       /etc/nginx/fastcgi_params;
        fastcgi_index index.php;
        fastcgi_param SCRIPT_FILENAME $document_root$fastcgi_script_name;
        fastcgi_pass  php;
    }
}
