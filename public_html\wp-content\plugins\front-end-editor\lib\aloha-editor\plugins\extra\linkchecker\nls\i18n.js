define({
	"root":  {
		"error.0": "An Error occurred checking this URL.",
		"error.400": "Bad Request. The request cannot be fulfilled due to bad syntax.",
		"error.401": "Unauthorized. Authentication has failed or not yet been provided.",
		"error.402": "Payment Required.",
		"error.403": "Forbidden. The request was a legal request, but the server is refusing to respond to it.",
		"error.404": "Not Found. The requested resource could not be found, but may be available in the future.",
		"error.405": "Method Not Allowed.",
		"error.406": "Not Acceptable. Your browser does not support the content.",
		"error.407": "Proxy Authentication Required.",
		"error.408": "Request Timeout. The server timed out waiting for the request.",
		"error.409": "Conflict in the request.",
		"error.410": "This resource is gone.",
		"error.411": "Length Required by server. This Link may work in the browsers.",
		"error.412": "Precondition Failed. This Link may work in the browsers.",
		"error.413": "Request Entity Too Large. The request is larger than the server is willing or able to process.",
		"error.414": "Request-URI Too Long. The URI provided was too long for the server to process.",
		"error.415": "Unsupported Media Type. The request entity has a media type which the server or resource does not support.",
		"error.416": "Requested Range Not Satisfiable. The client has asked for a portion of the file, but the server cannot supply that portion.",
		"error.417": "Expectation Failed. The server cannot meet the requirements of your browser.",
		"error.418": "I\'m a teapot. ;-)",
		"error.500": "Internal Server Error. A generic error message, given when no more specific message is suitable.",
		"error.501": "Not Implemented. The server either does not recognise the request method, or it lacks the ability to fulfill the request.",
		"error.502": "Bad Gateway. The server was acting as a gateway or proxy and received an invalid response from the upstream server.",
		"error.503": "Service Unavailable. The server is currently unavailable (because it is overloaded or down for maintenance). Generally, this is a temporary state.",
		"error.504": "Gateway Timeout. The server was acting as a gateway or proxy and did not receive a timely response from the upstream server.",
		"error.505": "HTTP Version Not Supported. The server does not support the HTTP protocol version used in the request."
	},
		"ca": true,
		"de": true,
		"mk": true,
		"pt-br": true,
		"ru": true,
		"uk": true,
		"zh-hans": true
});
