msgid ""
msgstr ""
"Project-Id-Version: Front-end Editor v0.8\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2012-07-11 23:16+0100\n"
"PO-Revision-Date: 2012-07-11 23:16+0100\n"
"Last-Translator: Li-An <<EMAIL>>\n"
"Language-Team: \n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=n != 1;\n"
"X-Poedit-Language: French\n"
"X-Poedit-Country: FRANCE\n"
"X-Poedit-SourceCharset: utf-8\n"
"X-Poedit-KeywordsList: __;_e;__ngettext:1,2;_n:1,2;__ngettext_noop:1,2;_n_noop:1,2;_c,_nc:4c,1,2;_x:1,2c;_ex:1,2c;_nx:4c,1,2;_nx_noop:4c,1,2\n"
"X-Poedit-Basepath: ../\n"
"X-Textdomain-Support: yes\n"
"X-Poedit-SearchPath-0: .\n"

# @ front-end-editor
#: front-end-editor.php:58
msgid "Title"
msgstr "Titre"

# @ front-end-editor
#: front-end-editor.php:65
msgid "Content"
msgstr "Contenu"

# @ front-end-editor
#: front-end-editor.php:71
msgid "Excerpt"
msgstr "Extrait"

# @ front-end-editor
#: front-end-editor.php:77
msgid "Custom fields"
msgstr "Champs personnalisés"

# @ front-end-editor
#: front-end-editor.php:83
msgid "Thumbnail"
msgstr "Miniature"

# @ front-end-editor
#: front-end-editor.php:91
msgid "Categories"
msgstr "Catégorie"

#: front-end-editor.php:97
msgid "Tags"
msgstr "Mots-clef"

#: front-end-editor.php:103
msgid "Terms"
msgstr "Termes"

# @ front-end-editor
#: front-end-editor.php:110
msgid "Text widget title"
msgstr "Titre de widget texte"

#: front-end-editor.php:117
msgid "Text widget content"
msgstr "Contenu de widget texte"

# @ front-end-editor
#: front-end-editor.php:124
msgid "Other widgets"
msgstr "Autres widgets"

# @ front-end-editor
#: front-end-editor.php:131
msgid "Comment text"
msgstr "Commentaires"

# @ front-end-editor
#: front-end-editor.php:137
msgid "Category title"
msgstr "Titre de catégorie"

# @ front-end-editor
#: front-end-editor.php:142
msgid "Tag title"
msgstr "Titre de tag"

# @ front-end-editor
#: front-end-editor.php:147
msgid "Term title"
msgstr "Titre de terme"

# @ front-end-editor
#: front-end-editor.php:152
msgid "Term description"
msgstr "Description de terme"

# @ front-end-editor
#: front-end-editor.php:159
msgid "Author description"
msgstr "Description de l'auteur"

# @ front-end-editor
#: front-end-editor.php:166
msgid "Site title and description"
msgstr "Titre du site et description"

# @ front-end-editor
#: front-end-editor.php:172
msgid "Site options"
msgstr "Options du site"

# @ front-end-editor
#: front-end-editor.php:178
msgid "Theme images"
msgstr "Images du thème"

# @ front-end-editor
#: admin/admin.php:9
msgid "Front-end Editor"
msgstr "Front-end Editor"

# @ front-end-editor
#: admin/admin.php:14
msgid "Fields"
msgstr "Champs"

# @ front-end-editor
#: admin/admin.php:15
#: lib/scb/AdminPage.php:346
msgid "Settings"
msgstr "Réglages"

# @ front-end-editor
#: admin/admin.php:22
msgid "Enable the WYSIWYG editor."
msgstr "Activer l'éditeur WYSIWYG (visuel)."

#: admin/admin.php:29
msgid "Edit all post fields at once."
msgstr "Éditer tous les champs de billet en une fois."

#: admin/admin.php:37
msgid "dropdown"
msgstr "menu déroulant"

# @ front-end-editor
#: admin/admin.php:38
msgid "text field"
msgstr "champ de texte"

# @ front-end-editor
#: admin/admin.php:40
msgid "To edit categories, use a:"
msgstr "Pour éditer les catégories, utiliser un:"

# @ front-end-editor
#: admin/admin.php:74
msgid "Enable or disable editable fields:"
msgstr "Activer ou désactiver les champs d'édition:"

# @ front-end-editor
#: admin/admin.php:76
msgid "Post fields"
msgstr "Champs des billets"

# @ front-end-editor
#: admin/admin.php:77
msgid "Other fields"
msgstr "Autres champs"

# @ front-end-editor
#: lib/scb/AdminPage.php:172
msgid "Settings <strong>saved</strong>."
msgstr "Réglages <strong>sauvegardés</strong>."

# @ front-end-editor
#: lib/scb/AdminPage.php:185
#: lib/scb/AdminPage.php:196
msgid "Save Changes"
msgstr "Sauvegarder les modifications"

# @ front-end-editor
#: php/core.php:43
msgid "Edit"
msgstr "Éditer"

# @ front-end-editor
#: php/core.php:44
msgid "Save"
msgstr "Sauvegarder"

# @ front-end-editor
#: php/core.php:45
msgid "Cancel"
msgstr "Annuler"

# @ front-end-editor
#: php/core.php:68
msgid "Change Image"
msgstr "Changer d'image"

# @ front-end-editor
#: php/core.php:69
msgid "Insert Image"
msgstr "Insérer une image"

# @ front-end-editor
#: php/core.php:70
msgid "Clear"
msgstr "Nettoyer"

#: php/core.php:268
msgid "Undefined field"
msgstr "Champ non défini"

#: php/core.php:272
msgid "Insufficient permissions"
msgstr "Permissions insuffisantes"

# @ front-end-editor
#: php/fields/base.php:138
msgid "empty"
msgstr "vide"

# @ front-end-editor
#: php/fields/post.php:101
#, php-format
msgid "Error: %s is currently editing this."
msgstr "Erreur: %s est en train d'éditer ceci."

#~ msgid "Begin editing using existing edit link (experimental)."
#~ msgstr "Commencer à éditer en utilisant les liens d'édition (expérimental)."
