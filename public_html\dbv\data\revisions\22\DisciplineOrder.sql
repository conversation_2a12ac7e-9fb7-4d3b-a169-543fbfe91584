ALTER TABLE `disciplines` ADD `order` tinyint unsigned NOT NULL;

UPDATE `disciplines` SET `order` = '1' WHERE `name` = 'F Standard';
UPDATE `disciplines` SET `order` = '2' WHERE `name` = 'F Open';
UPDATE `disciplines` SET `order` = '3' WHERE `name` = 'F/TR';
UPDATE `disciplines` SET `order` = '4' WHERE `name` = 'Target Rifle';
UPDATE `disciplines` SET `order` = '5' WHERE `name` = 'Optical';
UPDATE `disciplines` SET `order` = '6' WHERE `name` = 'NT F CLASS STANDARD';
UPDATE `disciplines` SET `order` = '7' WHERE `name` = 'WA Division System';

ALTER TABLE `grades` ADD `order` tinyint unsigned NOT NULL;

-- F Standard
UPDATE `grades` SET `order` = '1' WHERE `id` = '4';
UPDATE `grades` SET `order` = '2' WHERE `id` = '5';

-- F Open
UPDATE `grades` SET `order` = '3' WHERE `id` = '6';

-- F/TR
UPDATE `grades` SET `order` = '4' WHERE `id` = '7';

-- Target Rifle
UPDATE `grades` SET `order` = '5' WHERE `id` = '3';
UPDATE `grades` SET `order` = '6' WHERE `id` = '2';
UPDATE `grades` SET `order` = '7' WHERE `id` = '1';

-- Optical
UPDATE `grades` SET `order` = '8' WHERE `id` = '8';

-- WA Divisions
UPDATE `grades` SET `order` = '9' WHERE `id` = '9';
UPDATE `grades` SET `order` = '10' WHERE `id` = '10';
UPDATE `grades` SET `order` = '11' WHERE `id` = '11';
UPDATE `grades` SET `order` = '12' WHERE `id` = '12';
