DROP PROCEDURE IF EXISTS MergeShooters;
CREATE PROCEDURE MergeShooters(IN fromSid INT, IN toSid INT)
  BEGIN
    START TRANSACTION;
    -- Need their IDs
    SELECT id into @fromId FROM shooters WHERE sid = fromSid;
    SELECT id into @toId FROM shooters WHERE sid = toSid;

    -- Shooter details
    -- TODO: Possibly update their details but its hard to say if this is viable
    UPDATE shooter_secondary_memberships SET shooter_id = @toId WHERE shooter_id = @fromId;

    -- Event stuff
    UPDATE event_teams_shooters SET shooter_id = @toId WHERE shooter_id = @fromId;
    UPDATE event_entry_forms SET shooter_id = @toId WHERE shooter_id = @fromId;

    -- Results
    UPDATE results SET shooter_id = @toId WHERE shooter_id = @fromId;

    -- Audit Trails
    UPDATE audit_trails SET row_id = @toId WHERE row_id = @fromId AND table_name = "shooters";
    UPDATE audit_trails SET row_id = @toId WHERE row_id = @fromId AND table_name LIKE "shooter_%";
    UPDATE audit_trail_changes SET `to` = @toId WHERE `to` = @fromId AND column_name = "shooter_id";

    -- Need to manually delete fromSid for safety
    COMMIT;
  END;
