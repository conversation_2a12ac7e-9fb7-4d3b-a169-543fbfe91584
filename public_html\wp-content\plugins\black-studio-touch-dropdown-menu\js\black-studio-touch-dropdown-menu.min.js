/* Black Studio Touch Dropdown Menu */
!function(a){var b=black_studio_touch_dropdown_menu_params,c="ontouchstart"in window||window.navigator.msPointerEnabled,d=/iPad|iPod|iPhone/.test(navigator.platform)&&"matchMedia"in window,e=b.force_ios5,f=!1;!c||d&&!e||a(document).ready(function(){a(b.selector).each(function(){var c=a(this);c.attr("aria-haspopup",!0),c.data("dataNoclick",!1),c.bind("touchstart",function(){var d,e;if(!f&&void 0!==a.fn.superfish){for(d=0;d<a.fn.superfish.o.length;d++)a.fn.superfish.o[d].delay=800;f=!0}e=!c.data("dataNoclick"),a(b.selector).each(function(){a(this).data("dataNoclick",!1)}),c.data("dataNoclick",e),this.focus()}),c.bind("click",function(a){c.data("dataNoclick")&&a.preventDefault(),this.focus()})}),a(b.selector_leaf).each(function(){a(this).bind("touchstart",function(){window.location=this.href})})})}(jQuery);