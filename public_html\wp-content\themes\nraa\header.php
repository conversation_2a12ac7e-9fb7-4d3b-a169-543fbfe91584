<!DOCTYPE html>
<html <?php language_attributes(); ?>>
    <head>
        <meta charset="<?php bloginfo('charset'); ?>">
        <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
        <title><?php bloginfo('name') ?> | <?php is_home() ? bloginfo('description') : wp_title(''); ?></title>
        <meta name="viewport" content="width=device-width">

        <link rel="profile" href="http://gmpg.org/xfn/11">
        <link rel="pingback" href="<?php bloginfo('pingback_url'); ?>">

		<!-- Global site tag (gtag.js) - Google Analytics -->
<script async src="https://www.googletagmanager.com/gtag/js?id=G-B2VGRRSLZW"></script>
<script>
  window.dataLayer = window.dataLayer || [];
  function gtag(){dataLayer.push(arguments);}
  gtag('js', new Date());

  gtag('config', 'G-B2VGRRSLZW');
</script>

		
		
        <script>
          (function() {
            var cx = '012119197858155142986:tupgfihpftw';
            var gcse = document.createElement('script');
            gcse.type = 'text/javascript';
            gcse.async = true;
            gcse.src = (document.location.protocol == 'https:' ? 'https:' : 'http:') +
                '//www.google.com/cse/cse.js?cx=' + cx;
            var s = document.getElementsByTagName('script')[0];
            s.parentNode.insertBefore(gcse, s);
          })();
        </script>
        <style>
          div#___gcse_0 { width: 35%; float: right; margin-top: 85px; }
          div#___gcse_0 .gsc-control-cse { padding: 0; }
          div#___gcse_0 form.gsc-search-box { padding: 0; }
        </style>

        <?php wp_head(); ?>
<style>
    .page-container > header { background: url('<?php header_image(); ?>') no-repeat bottom center; width: <?php echo get_custom_header()->width; ?>px; height: <?php echo get_custom_header()->height; ?>px; }
</style>
<!--[if lt IE 8]>
    <style>hr { border-top: 1px solid #d6d7d9; border-bottom: 1px solid #ffffff; }</style>
    <script src="//cdnjs.cloudflare.com/ajax/libs/json3/3.2.4/json3.min.js"></script>
<![endif]-->
    </head>
    <!--[if lt IE 7]> <body <?php body_class('no-js lt-ie9 lt-ie8 lt-ie7'); ?> lang="en"> <![endif]-->
    <!--[if IE 7]> <body <?php body_class('no-js lt-ie9 lt-ie8'); ?> lang="en"> <![endif]-->
    <!--[if IE 8]>    <body <?php body_class('no-js lt-ie9'); ?> lang="en"> <![endif]-->
    <!--[if gt IE 8]><!--> <body <?php body_class('no-js'); ?> lang="en"> <!--<![endif]-->
        <!-- Prompt IE 6 users to install Chrome Frame. Remove this if you support IE 6.
            chromium.org/developers/how-tos/chrome-frame-getting-started -->
        <!--[if lt IE 7]><p class=chromeframe>Your browser is <em>ancient!</em> <a href="http://browsehappy.com/">Upgrade to a different browser</a> or <a href="http://www.google.com/chromeframe/?redirect=true">install Google Chrome Frame</a> to experience this site.</p><![endif]-->
        <div class="page-container">
            <header>
              <gcse:search></gcse:search>
            </header>
            <?php wp_nav_menu(array(
                'sort_column' => 'menu_order',
                'menu_class' => 'nav',
                'theme_location' => 'primary-menu',
                'fallback_cb' => false
            )); ?>
            <div class="content-container">
