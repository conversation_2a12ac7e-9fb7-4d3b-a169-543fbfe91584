CREATE TABLE `event_entry_forms` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `event_id` int(10) unsigned NOT NULL,
  `shooter_id` int(10) unsigned NOT NULL,
  `grade_id` int(10) unsigned DEFAULT NULL,
  `entry_number` int(11) NOT NULL,
  `status` enum('new','paid','withdrawn') NOT NULL,
  `payment_method` enum('cash','cheque','money order','visa','master card') NOT NULL,
  `cc_name` varchar(255) DEFAULT NULL,
  `cc_number` varchar(255) DEFAULT NULL,
  `cc_expiry` varchar(255) DEFAULT NULL,
  `cc_security_code` int(3) DEFAULT NULL,
  `notes` text NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `entry_number` (`entry_number`,`event_id`),
  UNIQUE KEY `unique event-shooter` (`event_id`,`shooter_id`),
  KEY `shooter_fk` (`shooter_id`),
  KEY `event_fk` (`event_id`),
  KEY `grade_fk` (`grade_id`),
  CONSTRAINT `event_entry_forms_ibfk_1` FOREIGN KEY (`event_id`) REFERENCES `events` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
  CONSTRAINT `event_entry_forms_ibfk_2` FOREIGN KEY (`shooter_id`) REFERENCES `shooters` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
  CONSTRAINT `event_entry_forms_ibfk_4` FOREIGN KEY (`grade_id`) REFERENCES `grades` (`id`) ON DELETE SET NULL ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=latin1