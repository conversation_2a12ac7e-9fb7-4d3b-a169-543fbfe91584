define({
	"error.0": "<PERSON>im Überprüfen der URL ist ein Fehler aufgetreten.",
	"error.400": "Bad Request. Die Anfrage enthält Syntaxfehler. Der Server kann die Anfrage deshalb nicht bearbeiten.",
	"error.401": "Unauthorized.  Die angeforderten Daten sind zugangsgeschützt.",
	"error.402": "Payment Required. Die angeforderten Daten sind kostenpflichtig.",
	"error.403": "Forbidden. Der Server möchte die angeforderten Daten nicht herausgeben.",
	"error.404": "Not found. Der angeforderte URI existiert zur Zeit nicht, kann aber in Zukunft verfügbar sein.",
	"error.405": "Method Not Allowed. Die angegebene Übertragungsmethode ist auf dem Server nicht erlaubt.",
	"error.406": "Not Acceptable. Die Anfrage ist in dieser Form nicht akzeptabel.",
	"error.407": "Proxy Authentication Required. Die Anfrage soll über einen Proxy-Server geleitet werden. Der Proxy-Server ist aber zugangsgeschützt und leitet Anfragen erst nach dem Erhalt einer gültigen Zugangskennung weiter",
	"error.408": "Request Timeout. Der Server hat eine erwartete Anfrage nicht innerhalb des dafür festgelegten Maximalzeitraums erhalten.",
	"error.409": "Conflict. Der Server kann die angeforderten Daten nicht senden, weil ein Konflikt mit einem anderen Prozess aufgetaucht ist.",
	"error.410": "Gone. Die angeforderten Daten wurden zu einem anderen URI verschoben. Dem Server ist aber nicht bekannt, wohin.",
	"error.411": "Length Required. Die Daten werden nicht gesendet. Sie können nur gesendet werden, wenn die Anfrage eine Angabe zu content-length enthält.",
	"error.412": "Precondition Failed. Eine oder mehrere Bedingungen, die bei der Anfrage gestellt wurden, treffen nicht zu.",
	"error.413": "Request Entity Too Large. Der Server kann die Anfrage nicht bearbeiten, weil diese zu viele Zeichen enthält.",
	"error.414": "Request-URL Too Long. Der Server kann die Anfrage nicht bearbeiten, weil die angeforderte Adresse zu viele Zeichen enthält.",
	"error.415": "Unsupported Media Type. Der Server will die Anfrage nicht bearbeiten, weil der MIME-Typ, den der Client in der Anfrage verwendet hat, für diese Anfrage nicht unterstützt wird.",
	"error.416": "Requested Range Not Satisfiable. Die Anfrage enthält Angaben, welcher Byte-Bereich von dem angeforderten URI übertragen werden soll.",
	"error.417": "Expectation Failed. Die Anfrage enthält im expect-Feld bestimmte Wünsche, die der Server nicht erfüllen kann.",
	"error.418": "Ich bin eine Teekanne. ;-)",
	"error.500": "Internal Server Error. Der Server kann die angeforderten Daten nicht senden, weil auf dem Server ein Fehler aufgetreten ist.",
	"error.501": "Not Implemented. Die Anfrage enthält Anforderungen, die der Server nicht bearbeiten kann, weil die Voraussetzungen dazu nicht implementiert sind.",
	"error.502": "Bad Gateway. Zum Bearbeiten der Anfrage musste der Server einen anderen Server aufrufen, erhielt dabei jedoch eine Fehlermeldung.",
	"error.503": "Service Unavailable. Der Server kann die Anfrage wegen Überlastung nicht bearbeiten. Normalerweise ist das ein temporärer Zustand.",
	"error.504": "Gateway Timeout. Zum Bearbeiten der Anfrage musste der Server einen anderen Server aufrufen, erhielt dabei jedoch nach einem festgelegten Maximalzeitraum keine Antwort.",
	"error.505": "HTTP Version Not Supported. Der Server unterstützt die im HTTP-Header der Anfrage angegebene HTTP-Version nicht."
});
