/*! This file is auto-generated */
!function(o){window.findPosts={open:function(n,e){var i=o(".ui-find-overlay");return 0===i.length&&(o("body").append('<div class="ui-find-overlay"></div>'),findPosts.overlay()),i.show(),n&&e&&o("#affected").attr("name",n).val(e),o("#find-posts").show(),o("#find-posts-input").trigger("focus").on("keyup",function(n){27==n.which&&findPosts.close()}),findPosts.send(),!1},close:function(){o("#find-posts-response").empty(),o("#find-posts").hide(),o(".ui-find-overlay").hide()},overlay:function(){o(".ui-find-overlay").on("click",function(){findPosts.close()})},send:function(){var n={ps:o("#find-posts-input").val(),action:"find_posts",_ajax_nonce:o("#_ajax_nonce").val()},e=o(".find-box-search .spinner");e.addClass("is-active"),o.ajax(ajaxurl,{type:"POST",data:n,dataType:"json"}).always(function(){e.removeClass("is-active")}).done(function(n){n.success||o("#find-posts-response").text(wp.i18n.__("An error has occurred. Please reload the page and try again.")),o("#find-posts-response").html(n.data)}).fail(function(){o("#find-posts-response").text(wp.i18n.__("An error has occurred. Please reload the page and try again."))})}},o(document).ready(function(){var n,e=o("#wp-media-grid");e.length&&window.wp&&window.wp.media&&(n=_wpMediaGridSettings,n=window.wp.media({frame:"manage",container:e,library:n.queryVars}).open(),e.trigger("wp-media-grid-ready",n)),o("#find-posts-submit").on("click",function(n){o('#find-posts-response input[type="radio"]:checked').length||n.preventDefault()}),o("#find-posts .find-box-search :input").on("keypress",function(n){if(13==n.which)return findPosts.send(),!1}),o("#find-posts-search").on("click",findPosts.send),o("#find-posts-close").on("click",findPosts.close),o("#doaction").on("click",function(e){o('select[name="action"]').each(function(){var n=o(this).val();"attach"===n?(e.preventDefault(),findPosts.open()):"delete"===n&&(showNotice.warn()||e.preventDefault())})}),o(".find-box-inside").on("click","tr",function(){o(this).find(".found-radio input").prop("checked",!0)})})}(jQuery);