define({
	"error.0": "При проверке этого URL произошла ошибка.",
	"error.400": "Неверный запрос. Запрос не может быть выполнен в связи с неправильным синтаксисом.",
	"error.401": "Отказано в авторизации. Аутентификация не удалась или в данное время невозможна.",
	"error.402": "Оплата обязательна.",
	"error.403": "Запрещено. Запрос правилен, но сервер не может на него ответить.",
	"error.404": "Не найдено. Искомый ресурс не может быть найден сейчас, но может быть доступен в будущем.",
	"error.405": "Метод не разрешен.",
	"error.406": "Не принято. Ваш браузер не поддерживает данный вид контента.",
	"error.407": "Аутентификация по прокси обязательна.",
	"error.408": "Истекло время ожидания. Сервер задерживает ответ ожидая запрос.",
	"error.409": "Конфликт в запросе.",
	"error.410": "Ресурс был удален.",
	"error.411": "Необходима длина. Ссылка может работать в браузерах.",
	"error.412": "Условие ложно. Ссылка может работать в браузерах.",
	"error.413": "Размер запроса слишком велик. Запрос больше, чем сервер в требует или может обработать.",
	"error.414": "Запрашиваемый URI слишком длинный. Предоставленный URI слшиком длинный чтобы сервер его обработал.",
	"error.415": "Неподдерживаемый тип данных. Запрошенная сущность имеет тип данных, который сервер или ответ на поддерживает.",
	"error.416": "Запрашиваемый диапазон не достижим. Клиент запросил часть файла, но сервер не может вернуть эту часть.",
	"error.417": "Ожидаемое неприемлимо. Сервер не может удовлетворить требования вашего браузера.",
	"error.418": "А я чайник :-)",
	"error.500": "Внутренняя ошибка сервера. Общее сообщение об ошике, выдается когда нет другого подходящего сообщения.",
	"error.501": "Не реализовано. Сервер не распознал метод запроса или не может удовлетворить запрос.",
	"error.502": "Ошибочный шлюз. Сервер работает в роли шлюза или прокси и получил неправильный ответ от вышестоящего сервера.",
	"error.503": "Сервис недоступен. Сервер временно недосутпен (потому что перегружен или закрыт на техническое обслуживание). Как правило, это временное состояние.",
	"error.504": "Шлюз не отвечает. Сервер выполняет роль шлюза или прокси и не получил вовремя ответ от вышестоящего сервера.",
	"error.505": "Версия HTTP не поддерживается. Сервер не поддерживает версию протокола HTTP переданную в запросе."
});
