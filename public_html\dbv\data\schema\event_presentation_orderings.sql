CREATE TABLE `event_presentation_orderings` (
  `event_id` int(10) unsigned NOT NULL,
  `grade_id` int(10) unsigned NOT NULL,
  `order` tinyint(4) NOT NULL,
  UNIQUE KEY `event_id_grade_id` (`event_id`,`grade_id`),
  UNIQUE KEY `event_id_order` (`event_id`,`order`),
  KEY `grade_id` (`grade_id`),
  CONSTRAINT `event_presentation_orderings_ibfk_1` FOREIGN KEY (`event_id`) REFERENCES `events` (`id`),
  CONSTRAINT `event_presentation_orderings_ibfk_2` FOREIGN KEY (`grade_id`) REFERENCES `grades` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1