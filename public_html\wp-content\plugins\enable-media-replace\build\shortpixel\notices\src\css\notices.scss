
.shortpixel.notice
{
   //padding: 18px;
   //min-height: 50px;
   padding: 8px;
   img
   {
     display:inline-block;
     margin: 0 25px 0 0;
     max-height: 50px;
   }
   .notice-dismiss
   {
     margin-top: 10px;
   }
}

/* In-view notice ( not on top, between the options ) - styled after WP notice */
.view-notice
{

  box-shadow: 0 1px 1px 0 rgba( 0, 0, 0, 0.1 );
  border: 4px solid #fff;

  padding: 1px 12px;
  p {
    margin: 1em 0 !important;
  }
  &.warning
  {
    border-left-color: #ffb900;
  }
}

.view-notice-row
{
  display: none;
}
