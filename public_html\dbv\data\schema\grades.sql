CREATE TABLE `grades` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `discipline_id` int(10) unsigned NOT NULL,
  `threshold` decimal(10,2) NOT NULL COMMENT 'This represents a percentage!',
  `name` varchar(255) NOT NULL,
  PRIMARY KEY (`id`),
  <PERSON>EY `discipline_id` (`discipline_id`),
  CONSTRAINT `grades_ibfk_2` FOREIGN KEY (`discipline_id`) REFERENCES `disciplines` (`id`) ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=latin1