define({
	"floatingmenu.tab.table": "Tabela",
	"floatingmenu.tab.tablelayout": "Layout da tabela",
	"deleterows.confirm": "Deseja realmente remover as linhas selecionadas?",
	"deletecolumns.confirm": "Deseja realmente remover as colunas selecionadas?",
	"deletetable.confirm": "Deseja realmente remover a tabela?",
	"Table": "Tabela",
	"button.createtable.tooltip": "Inserir Tabela",
	"button.addcolleft.tooltip": "Adicionar Coluna à esquerda",
	"button.addcolright.tooltip": "Adicionar Coluna à direita",
	"button.delcols.tooltip": "Remover Colunas",
	"button.addrowbefore.tooltip": "Adicionar <PERSON> antes",
	"button.addrowafter.tooltip": "Adicionar Linha depois",
	"button.delrows.tooltip": "Remover Linhas",
	"button.caption.tooltip": "Legendas da Tabela",
	"empty.caption": "Legendas da Tabela",
	"button.removeFormat.tooltip": "Remover formatação",
	"button.removeFormat.text": "Remover formatação",
	"button.rowheader.tooltip": "Formatar de linha como cabeçalho da tabela",
	"button.columnheader.tooltip": "Formatar coluna como cabeçalho da tabela",
	"button.mergecells.tooltip": "Mesclar células",
	"button.splitcells.tooltip": "Dividir células",
	"table.label.target": "Sumário",
	"table.sidebar.title": "Tabela",
	"table.mergeCells.notRectangular": "Apenas uma seleção retangular podem ser fundidas",
	"table.addColumns.nonConsecutive": "Por favor selecione uma única coluna ou um intervalo consecutivo de colunas",
	"table.createTable.nestedTablesNoSupported": "Desculpe, tabelas aninhadas não são suportadas"
});
