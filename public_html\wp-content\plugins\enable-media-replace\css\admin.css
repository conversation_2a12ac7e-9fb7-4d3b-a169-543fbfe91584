.ui-widget-content {
  border: 1px solid #dddddd;
  background: #ffffff;
  color: #333333; }

.ui-widget-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%; }

.ui-datepicker {
  width: 17em;
  padding: .2em .2em 0;
  display: none; }

.ui-datepicker .ui-datepicker-header {
  position: relative;
  padding: .2em 0; }

.ui-datepicker .ui-datepicker-prev,
.ui-datepicker .ui-datepicker-next {
  position: absolute;
  top: 2px;
  width: 1.8em;
  height: 1.8em; }

.ui-datepicker .ui-datepicker-prev-hover,
.ui-datepicker .ui-datepicker-next-hover {
  top: 1px; }

.ui-datepicker .ui-datepicker-prev {
  left: 2px; }

.ui-datepicker .ui-datepicker-next {
  right: 2px; }

.ui-datepicker .ui-datepicker-prev-hover {
  left: 1px; }

.ui-datepicker .ui-datepicker-next-hover {
  right: 1px; }

.ui-datepicker .ui-datepicker-prev span,
.ui-datepicker .ui-datepicker-next span {
  display: block;
  position: absolute;
  left: 50%;
  margin-left: -8px;
  top: 50%;
  margin-top: -8px; }

.ui-datepicker .ui-datepicker-title {
  margin: 0 2.3em;
  line-height: 1.8em;
  text-align: center; }

.ui-datepicker .ui-datepicker-title select {
  font-size: 1em;
  margin: 1px 0; }

.ui-datepicker select.ui-datepicker-month,
.ui-datepicker select.ui-datepicker-year {
  width: 45%; }

.ui-datepicker table {
  width: 100%;
  font-size: .9em;
  border-collapse: collapse;
  margin: 0 0 .4em; }

.ui-datepicker th {
  padding: .7em .3em;
  text-align: center;
  font-weight: bold;
  border: 0; }

.ui-datepicker td {
  border: 0;
  padding: 1px; }

.ui-datepicker td span,
.ui-datepicker td a {
  display: block;
  padding: .2em;
  text-align: center;
  text-decoration: none; }

.ui-datepicker .ui-datepicker-buttonpane {
  background-image: none;
  margin: .7em 0 0 0;
  padding: 0 .2em;
  border-left: 0;
  border-right: 0;
  border-bottom: 0; }

.ui-datepicker .ui-datepicker-buttonpane button {
  float: right;
  margin: .5em .2em .4em;
  cursor: pointer;
  padding: .2em .6em .3em .6em;
  width: auto;
  overflow: visible; }

.ui-datepicker .ui-datepicker-buttonpane button.ui-datepicker-current {
  float: left; }

/* with multiple calendars */
.ui-datepicker.ui-datepicker-multi {
  width: auto; }

.ui-datepicker-multi .ui-datepicker-group {
  float: left; }

.ui-datepicker-multi .ui-datepicker-group table {
  width: 95%;
  margin: 0 auto .4em; }

.ui-datepicker-multi-2 .ui-datepicker-group {
  width: 50%; }

.ui-datepicker-multi-3 .ui-datepicker-group {
  width: 33.3%; }

.ui-datepicker-multi-4 .ui-datepicker-group {
  width: 25%; }

.ui-datepicker-multi .ui-datepicker-group-last .ui-datepicker-header,
.ui-datepicker-multi .ui-datepicker-group-middle .ui-datepicker-header {
  border-left-width: 0; }

.ui-datepicker-multi .ui-datepicker-buttonpane {
  clear: left; }

.ui-datepicker-row-break {
  clear: both;
  width: 100%;
  font-size: 0; }

/* RTL support */
.ui-datepicker-rtl {
  direction: rtl; }

.ui-datepicker-rtl .ui-datepicker-prev {
  right: 2px;
  left: auto; }

.ui-datepicker-rtl .ui-datepicker-next {
  left: 2px;
  right: auto; }

.ui-datepicker-rtl .ui-datepicker-prev:hover {
  right: 1px;
  left: auto; }

.ui-datepicker-rtl .ui-datepicker-next:hover {
  left: 1px;
  right: auto; }

.ui-datepicker-rtl .ui-datepicker-buttonpane {
  clear: right; }

.ui-datepicker-rtl .ui-datepicker-buttonpane button {
  float: left; }

.ui-datepicker-rtl .ui-datepicker-buttonpane button.ui-datepicker-current,
.ui-datepicker-rtl .ui-datepicker-group {
  float: right; }

.ui-datepicker-rtl .ui-datepicker-group-last .ui-datepicker-header,
.ui-datepicker-rtl .ui-datepicker-group-middle .ui-datepicker-header {
  border-right-width: 0;
  border-left-width: 1px; }

/* Icons */
.ui-datepicker .ui-icon {
  display: block;
  text-indent: -99999px;
  overflow: hidden;
  background-repeat: no-repeat;
  left: .5em;
  top: .3em; }

.ui-icon {
  width: 16px;
  height: 16px; }

.ui-icon,
.ui-widget-content .ui-icon {
  background-image: url("../img/ui-icons_444444_256x240.png"); }

.ui-widget-header .ui-icon {
  background-image: url("../img/ui-icons_444444_256x240.png"); }

.ui-state-hover .ui-icon,
.ui-state-focus .ui-icon,
.ui-button:hover .ui-icon,
.ui-button:focus .ui-icon {
  background-image: url("../img/ui-icons_555555_256x240.png"); }

.ui-state-active .ui-icon,
.ui-button:active .ui-icon {
  background-image: url("../img/ui-icons_ffffff_256x240.png"); }

.ui-state-highlight .ui-icon,
.ui-button .ui-state-highlight.ui-icon {
  background-image: url("../img/ui-icons_777620_256x240.png"); }

/* positioning */
.ui-icon-blank {
  background-position: 16px 16px; }

.ui-icon-caret-1-n {
  background-position: 0 0; }

.ui-icon-caret-1-ne {
  background-position: -16px 0; }

.ui-icon-caret-1-e {
  background-position: -32px 0; }

.ui-icon-caret-1-se {
  background-position: -48px 0; }

.ui-icon-caret-1-s {
  background-position: -65px 0; }

.ui-icon-caret-1-sw {
  background-position: -80px 0; }

.ui-icon-caret-1-w {
  background-position: -96px 0; }

.ui-icon-caret-1-nw {
  background-position: -112px 0; }

.ui-icon-caret-2-n-s {
  background-position: -128px 0; }

.ui-icon-caret-2-e-w {
  background-position: -144px 0; }

.ui-icon-triangle-1-n {
  background-position: 0 -16px; }

.ui-icon-triangle-1-ne {
  background-position: -16px -16px; }

.ui-icon-triangle-1-e {
  background-position: -32px -16px; }

.ui-icon-triangle-1-se {
  background-position: -48px -16px; }

.ui-icon-triangle-1-s {
  background-position: -65px -16px; }

.ui-icon-triangle-1-sw {
  background-position: -80px -16px; }

.ui-icon-triangle-1-w {
  background-position: -96px -16px; }

.ui-icon-triangle-1-nw {
  background-position: -112px -16px; }

.ui-icon-triangle-2-n-s {
  background-position: -128px -16px; }

.ui-icon-triangle-2-e-w {
  background-position: -144px -16px; }

.ui-icon-arrow-1-n {
  background-position: 0 -32px; }

.ui-icon-arrow-1-ne {
  background-position: -16px -32px; }

.ui-icon-arrow-1-e {
  background-position: -32px -32px; }

.ui-icon-arrow-1-se {
  background-position: -48px -32px; }

.ui-icon-arrow-1-s {
  background-position: -65px -32px; }

.ui-icon-arrow-1-sw {
  background-position: -80px -32px; }

.ui-icon-arrow-1-w {
  background-position: -96px -32px; }

.ui-icon-arrow-1-nw {
  background-position: -112px -32px; }

.ui-icon-arrow-2-n-s {
  background-position: -128px -32px; }

.ui-icon-arrow-2-ne-sw {
  background-position: -144px -32px; }

.ui-icon-arrow-2-e-w {
  background-position: -160px -32px; }

.ui-icon-arrow-2-se-nw {
  background-position: -176px -32px; }

.ui-icon-arrowstop-1-n {
  background-position: -192px -32px; }

.ui-icon-arrowstop-1-e {
  background-position: -208px -32px; }

.ui-icon-arrowstop-1-s {
  background-position: -224px -32px; }

.ui-icon-arrowstop-1-w {
  background-position: -240px -32px; }

.ui-icon-arrowthick-1-n {
  background-position: 1px -48px; }

.ui-icon-arrowthick-1-ne {
  background-position: -16px -48px; }

.ui-icon-arrowthick-1-e {
  background-position: -32px -48px; }

.ui-icon-arrowthick-1-se {
  background-position: -48px -48px; }

.ui-icon-arrowthick-1-s {
  background-position: -64px -48px; }

.ui-icon-arrowthick-1-sw {
  background-position: -80px -48px; }

.ui-icon-arrowthick-1-w {
  background-position: -96px -48px; }

.ui-icon-arrowthick-1-nw {
  background-position: -112px -48px; }

.ui-icon-arrowthick-2-n-s {
  background-position: -128px -48px; }

.ui-icon-arrowthick-2-ne-sw {
  background-position: -144px -48px; }

.ui-icon-arrowthick-2-e-w {
  background-position: -160px -48px; }

.ui-icon-arrowthick-2-se-nw {
  background-position: -176px -48px; }

.ui-icon-arrowthickstop-1-n {
  background-position: -192px -48px; }

.ui-icon-arrowthickstop-1-e {
  background-position: -208px -48px; }

.ui-icon-arrowthickstop-1-s {
  background-position: -224px -48px; }

.ui-icon-arrowthickstop-1-w {
  background-position: -240px -48px; }

.ui-icon-arrowreturnthick-1-w {
  background-position: 0 -64px; }

.ui-icon-arrowreturnthick-1-n {
  background-position: -16px -64px; }

.ui-icon-arrowreturnthick-1-e {
  background-position: -32px -64px; }

.ui-icon-arrowreturnthick-1-s {
  background-position: -48px -64px; }

.ui-icon-arrowreturn-1-w {
  background-position: -64px -64px; }

.ui-icon-arrowreturn-1-n {
  background-position: -80px -64px; }

.ui-icon-arrowreturn-1-e {
  background-position: -96px -64px; }

.ui-icon-arrowreturn-1-s {
  background-position: -112px -64px; }

.ui-icon-arrowrefresh-1-w {
  background-position: -128px -64px; }

.ui-icon-arrowrefresh-1-n {
  background-position: -144px -64px; }

.ui-icon-arrowrefresh-1-e {
  background-position: -160px -64px; }

.ui-icon-arrowrefresh-1-s {
  background-position: -176px -64px; }

.ui-icon-arrow-4 {
  background-position: 0 -80px; }

.ui-icon-arrow-4-diag {
  background-position: -16px -80px; }

.ui-icon-extlink {
  background-position: -32px -80px; }

.ui-icon-newwin {
  background-position: -48px -80px; }

.ui-icon-refresh {
  background-position: -64px -80px; }

.ui-icon-shuffle {
  background-position: -80px -80px; }

.ui-icon-transfer-e-w {
  background-position: -96px -80px; }

.ui-icon-transferthick-e-w {
  background-position: -112px -80px; }

.ui-icon-folder-collapsed {
  background-position: 0 -96px; }

.ui-icon-folder-open {
  background-position: -16px -96px; }

.ui-icon-document {
  background-position: -32px -96px; }

.ui-icon-document-b {
  background-position: -48px -96px; }

.ui-icon-note {
  background-position: -64px -96px; }

.ui-icon-mail-closed {
  background-position: -80px -96px; }

.ui-icon-mail-open {
  background-position: -96px -96px; }

.ui-icon-suitcase {
  background-position: -112px -96px; }

.ui-icon-comment {
  background-position: -128px -96px; }

.ui-icon-person {
  background-position: -144px -96px; }

.ui-icon-print {
  background-position: -160px -96px; }

.ui-icon-trash {
  background-position: -176px -96px; }

.ui-icon-locked {
  background-position: -192px -96px; }

.ui-icon-unlocked {
  background-position: -208px -96px; }

.ui-icon-bookmark {
  background-position: -224px -96px; }

.ui-icon-tag {
  background-position: -240px -96px; }

.ui-icon-home {
  background-position: 0 -112px; }

.ui-icon-flag {
  background-position: -16px -112px; }

.ui-icon-calendar {
  background-position: -32px -112px; }

.ui-icon-cart {
  background-position: -48px -112px; }

.ui-icon-pencil {
  background-position: -64px -112px; }

.ui-icon-clock {
  background-position: -80px -112px; }

.ui-icon-disk {
  background-position: -96px -112px; }

.ui-icon-calculator {
  background-position: -112px -112px; }

.ui-icon-zoomin {
  background-position: -128px -112px; }

.ui-icon-zoomout {
  background-position: -144px -112px; }

.ui-icon-search {
  background-position: -160px -112px; }

.ui-icon-wrench {
  background-position: -176px -112px; }

.ui-icon-gear {
  background-position: -192px -112px; }

.ui-icon-heart {
  background-position: -208px -112px; }

.ui-icon-star {
  background-position: -224px -112px; }

.ui-icon-link {
  background-position: -240px -112px; }

.ui-icon-cancel {
  background-position: 0 -128px; }

.ui-icon-plus {
  background-position: -16px -128px; }

.ui-icon-plusthick {
  background-position: -32px -128px; }

.ui-icon-minus {
  background-position: -48px -128px; }

.ui-icon-minusthick {
  background-position: -64px -128px; }

.ui-icon-close {
  background-position: -80px -128px; }

.ui-icon-closethick {
  background-position: -96px -128px; }

.ui-icon-key {
  background-position: -112px -128px; }

.ui-icon-lightbulb {
  background-position: -128px -128px; }

.ui-icon-scissors {
  background-position: -144px -128px; }

.ui-icon-clipboard {
  background-position: -160px -128px; }

.ui-icon-copy {
  background-position: -176px -128px; }

.ui-icon-contact {
  background-position: -192px -128px; }

.ui-icon-image {
  background-position: -208px -128px; }

.ui-icon-video {
  background-position: -224px -128px; }

.ui-icon-script {
  background-position: -240px -128px; }

.ui-icon-alert {
  background-position: 0 -144px; }

.ui-icon-info {
  background-position: -16px -144px; }

.ui-icon-notice {
  background-position: -32px -144px; }

.ui-icon-help {
  background-position: -48px -144px; }

.ui-icon-check {
  background-position: -64px -144px; }

.ui-icon-bullet {
  background-position: -80px -144px; }

.ui-icon-radio-on {
  background-position: -96px -144px; }

.ui-icon-radio-off {
  background-position: -112px -144px; }

.ui-icon-pin-w {
  background-position: -128px -144px; }

.ui-icon-pin-s {
  background-position: -144px -144px; }

.ui-icon-play {
  background-position: 0 -160px; }

.ui-icon-pause {
  background-position: -16px -160px; }

.ui-icon-seek-next {
  background-position: -32px -160px; }

.ui-icon-seek-prev {
  background-position: -48px -160px; }

.ui-icon-seek-end {
  background-position: -64px -160px; }

.ui-icon-seek-start {
  background-position: -80px -160px; }

/* ui-icon-seek-first is deprecated, use ui-icon-seek-start instead */
.ui-icon-seek-first {
  background-position: -80px -160px; }

.ui-icon-stop {
  background-position: -96px -160px; }

.ui-icon-eject {
  background-position: -112px -160px; }

.ui-icon-volume-off {
  background-position: -128px -160px; }

.ui-icon-volume-on {
  background-position: -144px -160px; }

.ui-icon-power {
  background-position: 0 -176px; }

.ui-icon-signal-diag {
  background-position: -16px -176px; }

.ui-icon-signal {
  background-position: -32px -176px; }

.ui-icon-battery-0 {
  background-position: -48px -176px; }

.ui-icon-battery-1 {
  background-position: -64px -176px; }

.ui-icon-battery-2 {
  background-position: -80px -176px; }

.ui-icon-battery-3 {
  background-position: -96px -176px; }

.ui-icon-circle-plus {
  background-position: 0 -192px; }

.ui-icon-circle-minus {
  background-position: -16px -192px; }

.ui-icon-circle-close {
  background-position: -32px -192px; }

.ui-icon-circle-triangle-e {
  background-position: -48px -192px; }

.ui-icon-circle-triangle-s {
  background-position: -64px -192px; }

.ui-icon-circle-triangle-w {
  background-position: -80px -192px; }

.ui-icon-circle-triangle-n {
  background-position: -96px -192px; }

.ui-icon-circle-arrow-e {
  background-position: -112px -192px; }

.ui-icon-circle-arrow-s {
  background-position: -128px -192px; }

.ui-icon-circle-arrow-w {
  background-position: -144px -192px; }

.ui-icon-circle-arrow-n {
  background-position: -160px -192px; }

.ui-icon-circle-zoomin {
  background-position: -176px -192px; }

.ui-icon-circle-zoomout {
  background-position: -192px -192px; }

.ui-icon-circle-check {
  background-position: -208px -192px; }

.ui-icon-circlesmall-plus {
  background-position: 0 -208px; }

.ui-icon-circlesmall-minus {
  background-position: -16px -208px; }

.ui-icon-circlesmall-close {
  background-position: -32px -208px; }

.ui-icon-squaresmall-plus {
  background-position: -48px -208px; }

.ui-icon-squaresmall-minus {
  background-position: -64px -208px; }

.ui-icon-squaresmall-close {
  background-position: -80px -208px; }

.ui-icon-grip-dotted-vertical {
  background-position: 0 -224px; }

.ui-icon-grip-dotted-horizontal {
  background-position: -16px -224px; }

.ui-icon-grip-solid-vertical {
  background-position: -32px -224px; }

.ui-icon-grip-solid-horizontal {
  background-position: -48px -224px; }

.ui-icon-gripsmall-diagonal-se {
  background-position: -64px -224px; }

.ui-icon-grip-diagonal-se {
  background-position: -80px -224px; }

/* Corner radius */
.ui-corner-all,
.ui-corner-top,
.ui-corner-left,
.ui-corner-tl {
  border-top-left-radius: 3px; }

.ui-corner-all,
.ui-corner-top,
.ui-corner-right,
.ui-corner-tr {
  border-top-right-radius: 3px; }

.ui-corner-all,
.ui-corner-bottom,
.ui-corner-left,
.ui-corner-bl {
  border-bottom-left-radius: 3px; }

.ui-corner-all,
.ui-corner-bottom,
.ui-corner-right,
.ui-corner-br {
  border-bottom-right-radius: 3px; }

.emr_upload_form form {
  display: flex; }
  .emr_upload_form form .upsell-wrapper {
    margin-left: 10px; }
.emr_upload_form .wrapper {
  padding: 18px;
  border: 1px solid #ccc; }
  .emr_upload_form .wrapper .section-header {
    font-size: 18px;
    border-bottom: 1px solid #ccc;
    padding: 6px 0;
    margin: 0 0 15px 0; }
.emr_upload_form .image_chooser.wrapper {
  min-height: 350px; }
  .emr_upload_form .image_chooser.wrapper .emr_drop_area {
    border: 4px dashed #b4b9be;
    max-width: 600px;
    padding: 28px 14px;
    text-align: center;
    position: relative; }
    .emr_upload_form .image_chooser.wrapper .emr_drop_area h1 {
      display: none; }
    .emr_upload_form .image_chooser.wrapper .emr_drop_area .drop-wrapper {
      margin: 0 auto; }
    .emr_upload_form .image_chooser.wrapper .emr_drop_area.drop_breakout {
      position: fixed;
      left: 0;
      right: 0;
      bottom: 0;
      top: 0;
      max-width: none;
      border-color: #83b4d8;
      border-width: 10px;
      z-index: 999999;
      background-color: rgba(68, 68, 68, 0.7); }
      .emr_upload_form .image_chooser.wrapper .emr_drop_area.drop_breakout h1 {
        color: #fff;
        position: absolute;
        font-size: 50px;
        line-height: 50px;
        margin-top: -25px;
        top: 50%;
        width: 100%;
        text-align: center;
        display: block; }
      .emr_upload_form .image_chooser.wrapper .emr_drop_area.drop_breakout .drop-wrapper {
        display: none; }
  .emr_upload_form .image_chooser.wrapper .image_previews {
    margin: 15px 0; }
    .emr_upload_form .image_chooser.wrapper .image_previews .image_placeholder {
      position: relative;
      display: inline-block;
      margin-right: 25px;
      margin-bottom: 10px;
      border: 1px solid #ddd;
      vertical-align: top;
      max-height: 500px; }
      .emr_upload_form .image_chooser.wrapper .image_previews .image_placeholder .textlayer {
        font-size: 25px;
        line-height: 25px;
        opacity: 0.7;
        position: absolute;
        color: #ccc;
        left: 48%;
        top: 50%;
        transform: translate(-50%, -50%);
        border: 1px dashed #eee;
        background-color: #333;
        padding: 8px; }
      .emr_upload_form .image_chooser.wrapper .image_previews .image_placeholder .dashicons {
        font-size: 60px;
        position: absolute;
        top: 50%;
        margin-top: -30px;
        left: 50%;
        margin-left: -30px;
        opacity: 0.5; }
      .emr_upload_form .image_chooser.wrapper .image_previews .image_placeholder .image_size {
        text-align: center;
        position: absolute;
        bottom: -25px;
        width: 100%; }
      .emr_upload_form .image_chooser.wrapper .image_previews .image_placeholder.is_image .dashicons::before, .emr_upload_form .image_chooser.wrapper .image_previews .image_placeholder.is_image .dashicons {
        display: none; }
      .emr_upload_form .image_chooser.wrapper .image_previews .image_placeholder.not_image img {
        display: none; }
      .emr_upload_form .image_chooser.wrapper .image_previews .image_placeholder.not_image .textlayer {
        display: none; }
      .emr_upload_form .image_chooser.wrapper .image_previews .image_placeholder.not_image.is_document .textlayer {
        font-size: 18px;
        line-height: 20px;
        display: block; }
.emr_upload_form .form-error, .emr_upload_form .form-warning {
  background: #fff;
  padding: 8px;
  border-left: 4px solid #ff0000;
  margin: 10px 0;
  display: none; }
  .emr_upload_form .form-error p, .emr_upload_form .form-warning p {
    margin: 0;
    font-size: 12px;
    font-weight: 700; }
.emr_upload_form .form-warning {
  border-left: 4px solid #ffb900; }
.emr_upload_form .option-flex-wrapper {
  display: flex; }
.emr_upload_form .replace_type.wrapper {
  flex: 1;
  border: 1px solid #ccc;
  margin: 15px 0; }
  .emr_upload_form .replace_type.wrapper .option {
    position: relative;
    z-index: 1; }
    .emr_upload_form .replace_type.wrapper .option label {
      font-size: 1.2em; }
    .emr_upload_form .replace_type.wrapper .option .nofeature-notice {
      border: 1px solid #ccc;
      padding: 8px;
      margin: 0;
      position: absolute;
      left: 0;
      right: 0;
      top: 0;
      bottom: 0;
      opacity: 0.8;
      z-index: 9;
      background: #444; }
      .emr_upload_form .replace_type.wrapper .option .nofeature-notice p {
        text-align: center;
        color: #fff;
        margin: 15px 0; }
.emr_upload_form .options.wrapper {
  flex: 1;
  border: 1px solid #ccc;
  padding: 15px;
  margin: 15px 0 15px 35px; }
  .emr_upload_form .options.wrapper .custom_date .emr_datepicker {
    width: 150px; }
  .emr_upload_form .options.wrapper .custom_date .emr_hour, .emr_upload_form .options.wrapper .custom_date .emr_minute {
    width: 45px; }
  .emr_upload_form .options.wrapper ul li input {
    margin-right: 8px; }
  .emr_upload_form .options.wrapper .option label {
    vertical-align: top; }
  .emr_upload_form .options.wrapper .small {
    font-size: 10px;
    vertical-align: top;
    margin-left: 8px; }
  .emr_upload_form .options.wrapper .custom_date {
    margin: 8px 0 0 25px;
    visibility: hidden;
    opacity: 0; }
    .emr_upload_form .options.wrapper .custom_date span.field-title {
      display: inline-block;
      margin-bottom: 4px;
      color: #444;
      font-size: 12px;
      width: 100%;
      text-align: left;
      vertical-align: middle;
      line-height: 26px; }
      .emr_upload_form .options.wrapper .custom_date span.field-title::before {
        font-size: 20px;
        vertical-align: top;
        margin-right: 4px; }
  .emr_upload_form .options.wrapper .location_option {
    display: none;
    margin-top: 12px; }
    .emr_upload_form .options.wrapper .location_option label {
      vertical-align: baseline;
      margin-right: 8px; }
.emr_upload_form .form_controls.wrapper {
  clear: both;
  margin: 8px 0 15px 0;
  border: 0;
  padding: 0; }
  .emr_upload_form .form_controls.wrapper .button {
    padding-left: 20px;
    padding-right: 20px; }
.emr_upload_form .shortpixel.notice {
  padding: 12px; }
.emr_upload_form .shortpixel-offer {
  background: #fff;
  width: 250px;
  min-height: 270px;
  border: 1px solid #ccc;
  padding: 15px;
  margin-bottom: 25px;
  float: right;
  clear: both; }
  .emr_upload_form .shortpixel-offer h3 {
    line-height: 1.3em; }
  .emr_upload_form .shortpixel-offer.site-speed {
    background-color: #dcfdff; }
    .emr_upload_form .shortpixel-offer.site-speed .img-wrapper {
      text-align: center;
      margin: 0 0 25px 0; }
      .emr_upload_form .shortpixel-offer.site-speed .img-wrapper img {
        max-width: 140px;
        max-height: 140px;
        margin: 0; }
    .emr_upload_form .shortpixel-offer.site-speed h3 {
      color: #00d0e5;
      font-size: 20px;
      text-align: center;
      margin: 0;
      line-height: 1.3em; }
    .emr_upload_form .shortpixel-offer.site-speed .button-wrapper {
      text-align: center;
      margin-top: 35px; }
      .emr_upload_form .shortpixel-offer.site-speed .button-wrapper a {
        background-color: #ff0000;
        color: #fff;
        display: inline-block;
        padding: 8px;
        text-decoration: none;
        font-weight: 700;
        font-size: 20px; }
    .emr_upload_form .shortpixel-offer.site-speed .red {
      color: #ff0000; }
@media (max-width: 1200px) {
  .emr_upload_form .image_previews {
    text-align: center; }
  .emr_upload_form .option-flex-wrapper {
    flex-direction: column; }
    .emr_upload_form .option-flex-wrapper .options.wrapper {
      margin-left: 0; } }
@media (max-width: 960px) {
  .emr_upload_form .upsell-wrapper {
    display: none; } }
