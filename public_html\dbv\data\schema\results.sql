CREATE TABLE `results` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `match_id` int(10) unsigned NOT NULL,
  `shooter_id` int(10) unsigned NOT NULL,
  `grade_id` int(10) unsigned NOT NULL,
  `place` int(11) NOT NULL,
  `shots` varchar(255) DEFAULT NULL,
  `score_whole` int(11) NOT NULL,
  `score_partial` int(11) NOT NULL,
  PRIMARY KEY (`id`),
  KEY `shooter_id` (`shooter_id`),
  KEY `grade_id` (`grade_id`),
  KEY `match_id` (`match_id`),
  CONSTRAINT `results_ibfk_4` FOREIGN KEY (`match_id`) REFERENCES `matches` (`id`) ON UPDATE CASCADE,
  CONSTRAINT `results_ibfk_5` FOREIGN KEY (`shooter_id`) REFERENCES `shooters` (`id`) ON UPDATE CASCADE,
  CONSTRAINT `results_ibfk_6` FOREIGN KEY (`grade_id`) REFERENCES `grades` (`id`) ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=latin1