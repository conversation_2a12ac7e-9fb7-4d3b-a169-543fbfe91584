define({
	"button.strong.tooltip": "<PERSON>tt",
	"button.em.tooltip": "<PERSON>rs<PERSON>",
	"button.b.tooltip": "<PERSON><PERSON>",
	"button.i.tooltip": "<PERSON>rs<PERSON>",
	"button.u.tooltip": "Unterstrichen",
	"button.cite.tooltip": "Zitat",
	"button.q.tooltip": "Tooltip",
	"button.code.tooltip": "Code",
	"button.abbr.tooltip": "Abkürzung",
	"button.del.tooltip": "Durchgestrichen",
	"button.s.tooltip": "Durchgestrichen",
	"button.sub.tooltip": "Tiefgestellt",
	"button.sup.tooltip": "Hochgestellt",
	"button.p.tooltip": "Absatz",
	"button.h1.tooltip": "Überschrift 1",
	"button.h2.tooltip": "Überschrift 2",
	"button.h3.tooltip": "Überschrift 3",
	"button.h4.tooltip": "Überschrift 4",
	"button.h5.tooltip": "Überschrift 5",
	"button.h6.tooltip": "Überschrift 6",
	"button.pre.tooltip": "Vorformatierter text",
	"button.title.tooltip": "Titel",
	"button.removeFormat.tooltip": "Formatierung entfernen",
	"button.removeFormat.text": "Formatierung entfernen",
	"GENTICS_button_p": "GENTICS_button_p_de",
	"GENTICS_button_h1": "GENTICS_button_h1_de",
	"GENTICS_button_h2": "GENTICS_button_h2_de",
	"GENTICS_button_h3": "GENTICS_button_h3_de",
	"GENTICS_button_h4": "GENTICS_button_h4_de",
	"GENTICS_button_h5": "GENTICS_button_h5_de",
	"GENTICS_button_h6": "GENTICS_button_h6_de",
	"GENTICS_button_pre": "GENTICS_button_pre_de",
	"GENTICS_button_title": "GENTICS_button_title_de",
	"formatBold": "Ctrl+b",
	"formatItalic": "Ctrl+i",
	"formatUnderline": "Ctrl+u",
	"formatParagraph": "Alt+Ctrl+0",
	"formatH1": "Alt+Ctrl+1",
	"formatH2": "Alt+Ctrl+2",
	"formatH3": "Alt+Ctrl+3",
	"formatH4": "Alt+Ctrl+4",
	"formatH5": "Alt+Ctrl+5",
	"formatH6": "Alt+Ctrl+6",
	"formatPre": "Alt+Ctrl+P",
	"formatDel": "Ctrl+\\",
	"formatSub": "Ctrl+,",
	"formatSup": "Ctrl+.",
	"floatingmenu.tab.format": "Format",
	"format.class.legend": "CSS class",
	"format.class.none": "none"
});
