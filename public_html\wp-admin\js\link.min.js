/*! This file is auto-generated */
jQuery(document).ready(function(i){var t,c,e,a=!1;i("#link_name").trigger("focus"),postboxes.add_postbox_toggles("link"),i("#category-tabs a").on("click",function(){var t=i(this).attr("href");return i(this).parent().addClass("tabs").siblings("li").removeClass("tabs"),i(".tabs-panel").hide(),i(t).show(),"#categories-all"==t?deleteUserSetting("cats"):setUserSetting("cats","pop"),!1}),getUserSetting("cats")&&i('#category-tabs a[href="#categories-pop"]').trigger("click"),t=i("#newcat").one("focus",function(){i(this).val("").removeClass("form-input-tip")}),i("#link-category-add-submit").on("click",function(){t.focus()}),c=function(){var t,e;a||(a=!0,t=(e=i(this)).is(":checked"),e=e.val().toString(),i("#in-link-category-"+e+", #in-popular-link_category-"+e).prop("checked",t),a=!1)},e=function(t,e){i(e.what+" response_data",t).each(function(){i(i(this).text()).find("label").each(function(){var t=i(this),e=t.find("input").val(),a=t.find("input")[0].id,t=i.trim(t.text());i("#"+a).on("change",c),i('<option value="'+parseInt(e,10)+'"></option>').text(t)})})},i("#categorychecklist").wpList({alt:"",what:"link-category",response:"category-ajax-response",addAfter:e}),i('a[href="#categories-all"]').on("click",function(){deleteUserSetting("cats")}),i('a[href="#categories-pop"]').on("click",function(){setUserSetting("cats","pop")}),"pop"==getUserSetting("cats")&&i('a[href="#categories-pop"]').trigger("click"),i("#category-add-toggle").on("click",function(){return i(this).parents("div:first").toggleClass("wp-hidden-children"),i('#category-tabs a[href="#categories-all"]').trigger("click"),i("#newcategory").trigger("focus"),!1}),i(".categorychecklist :checkbox").on("change",c).filter(":checked").trigger("change")});