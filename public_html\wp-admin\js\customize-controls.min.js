/*! This file is auto-generated */
!function(Y){var a,e,s,t,n,i,o,G=wp.customize;G.OverlayNotification=G.Notification.extend({loading:!1,initialize:function(e,t){var n=this;G.Notification.prototype.initialize.call(n,e,t),n.containerClasses+=" notification-overlay",n.loading&&(n.containerClasses+=" notification-loading")},render:function(){var e=G.Notification.prototype.render.call(this);return e.on("keydown",_.bind(this.handleEscape,this)),e},handleEscape:function(e){var t=this;27===e.which&&(e.stopPropagation(),t.dismissible&&t.parent&&t.parent.remove(t.code))}}),G.Notifications=G.Values.extend({alt:!1,defaultConstructor:G.Notification,initialize:function(e){var t=this;G.Values.prototype.initialize.call(t,e),_.bindAll(t,"constrainFocus"),t._addedIncrement=0,t._addedOrder={},t.bind("add",function(e){t.trigger("change",e)}),t.bind("removed",function(e){t.trigger("change",e)})},count:function(){return _.size(this._value)},add:function(e,t){var n,i=this,e="string"==typeof e?(n=e,t):(n=e.code,e);return i.has(n)||(i._addedIncrement+=1,i._addedOrder[n]=i._addedIncrement),G.Values.prototype.add.call(i,n,e)},remove:function(e){return delete this._addedOrder[e],G.Values.prototype.remove.call(this,e)},get:function(e){var a,o=this,t=_.values(o._value);return _.extend({sort:!1},e).sort&&(a={error:4,warning:3,success:2,info:1},t.sort(function(e,t){var n=0,i=0;return(n=!_.isUndefined(a[e.type])?a[e.type]:n)!==(i=!_.isUndefined(a[t.type])?a[t.type]:i)?i-n:o._addedOrder[t.code]-o._addedOrder[e.code]})),t},render:function(){var e,n,t,i=this,a=!1,o=[],s={};i.container&&i.container.length&&(e=i.get({sort:!0}),i.container.toggle(0!==e.length),i.container.is(i.previousContainer)&&_.isEqual(e,i.previousNotifications)||((n=i.container.children("ul").first()).length||(n=Y("<ul></ul>"),i.container.append(n)),n.find("> [data-code]").remove(),_.each(i.previousNotifications,function(e){s[e.code]=e}),_.each(e,function(e){var t;!wp.a11y||s[e.code]&&_.isEqual(e.message,s[e.code].message)||wp.a11y.speak(e.message,"assertive"),t=Y(e.render()),e.container=t,n.append(t),e.extended(G.OverlayNotification)&&o.push(e)}),(t=Boolean(o.length))!==(a=i.previousNotifications?Boolean(_.find(i.previousNotifications,function(e){return e.extended(G.OverlayNotification)})):a)&&(Y(document.body).toggleClass("customize-loading",t),i.container.toggleClass("has-overlay-notifications",t),t?(i.previousActiveElement=document.activeElement,Y(document).on("keydown",i.constrainFocus)):Y(document).off("keydown",i.constrainFocus)),t?(i.focusContainer=o[o.length-1].container,i.focusContainer.prop("tabIndex",-1),((t=i.focusContainer.find(":focusable")).length?t.first():i.focusContainer).focus()):i.previousActiveElement&&(Y(i.previousActiveElement).focus(),i.previousActiveElement=null),i.previousNotifications=e,i.previousContainer=i.container,i.trigger("rendered")))},constrainFocus:function(e){var t,n=this;e.stopPropagation(),9===e.which&&(0===(t=n.focusContainer.find(":focusable")).length&&(t=n.focusContainer),!Y.contains(n.focusContainer[0],e.target)||!Y.contains(n.focusContainer[0],document.activeElement)||t.last().is(e.target)&&!e.shiftKey?(e.preventDefault(),t.first().focus()):t.first().is(e.target)&&e.shiftKey&&(e.preventDefault(),t.last().focus()))}}),G.Setting=G.Value.extend({defaults:{transport:"refresh",dirty:!1},initialize:function(e,t,n){var i=this,n=_.extend({previewer:G.previewer},i.defaults,n||{});G.Value.prototype.initialize.call(i,t,n),i.id=e,i._dirty=n.dirty,i.notifications=new G.Notifications,i.bind(i.preview)},preview:function(){var e=this,t=e.transport;"postMessage"===(t="postMessage"===t&&!G.state("previewerAlive").get()?"refresh":t)?e.previewer.send("setting",[e.id,e()]):"refresh"===t&&e.previewer.refresh()},findControls:function(){var n=this,i=[];return G.control.each(function(t){_.each(t.settings,function(e){e.id===n.id&&i.push(t)})}),i}}),G._latestRevision=0,G._lastSavedRevision=0,G._latestSettingRevisions={},G.bind("change",function(e){G._latestRevision+=1,G._latestSettingRevisions[e.id]=G._latestRevision}),G.bind("ready",function(){G.bind("add",function(e){e._dirty&&(G._latestRevision+=1,G._latestSettingRevisions[e.id]=G._latestRevision)})}),G.dirtyValues=function(n){var i={};return G.each(function(e){var t;e._dirty&&(t=G._latestSettingRevisions[e.id],G.state("changesetStatus").get()&&n&&n.unsaved&&(_.isUndefined(t)||t<=G._lastSavedRevision)||(i[e.id]=e.get()))}),i},G.requestChangesetUpdate=function(n,e){var t,i={},a=new Y.Deferred;return 0!==G.state("processing").get()?(a.reject("already_processing"),a.promise()):(t=_.extend({title:null,date:null,autosave:!1,force:!1},e),n&&_.extend(i,n),_.each(G.dirtyValues({unsaved:!0}),function(e,t){n&&null===n[t]||(i[t]=_.extend({},i[t]||{},{value:e}))}),G.trigger("changeset-save",i,t),!t.force&&_.isEmpty(i)&&null===t.title&&null===t.date?(a.resolve({}),a.promise()):t.status?a.reject({code:"illegal_status_in_changeset_update"}).promise():t.date&&t.autosave?a.reject({code:"illegal_autosave_with_date_gmt"}).promise():(G.state("processing").set(G.state("processing").get()+1),a.always(function(){G.state("processing").set(G.state("processing").get()-1)}),delete(e=G.previewer.query({excludeCustomizedSaved:!0})).customized,_.extend(e,{nonce:G.settings.nonce.save,customize_theme:G.settings.theme.stylesheet,customize_changeset_data:JSON.stringify(i)}),null!==t.title&&(e.customize_changeset_title=t.title),null!==t.date&&(e.customize_changeset_date=t.date),!1!==t.autosave&&(e.customize_changeset_autosave="true"),G.trigger("save-request-params",e),(e=wp.ajax.post("customize_save",e)).done(function(e){var n={};G._lastSavedRevision=Math.max(G._latestRevision,G._lastSavedRevision),G.state("changesetStatus").set(e.changeset_status),e.changeset_date&&G.state("changesetDate").set(e.changeset_date),a.resolve(e),G.trigger("changeset-saved",e),e.setting_validities&&_.each(e.setting_validities,function(e,t){!0===e&&_.isObject(i[t])&&!_.isUndefined(i[t].value)&&(n[t]=i[t].value)}),G.previewer.send("changeset-saved",_.extend({},e,{saved_changeset_values:n}))}),e.fail(function(e){a.reject(e),G.trigger("changeset-error",e)}),e.always(function(e){e.setting_validities&&G._handleSettingValidities({settingValidities:e.setting_validities})}),a.promise()))},G.utils.bubbleChildValueChanges=function(n,e){Y.each(e,function(e,t){n[t].bind(function(e,t){n.parent&&e!==t&&n.parent.trigger("change",n)})})},e=function(e){var t,n,i=this,a=function(){var e=(i.extended(G.Panel)||i.extended(G.Section))&&i.expanded&&i.expanded()?i.contentContainer:i.container;(n=0===(n=e.find(".control-focus:first")).length?e.find("input, select, textarea, button, object, a[href], [tabindex]").filter(":visible").first():n).focus()};(e=e||{}).completeCallback?(t=e.completeCallback,e.completeCallback=function(){a(),t()}):e.completeCallback=a,G.state("paneVisible").set(!0),i.expand?i.expand(e):e.completeCallback()},G.utils.prioritySort=function(e,t){return e.priority()===t.priority()&&"number"==typeof e.params.instanceNumber&&"number"==typeof t.params.instanceNumber?e.params.instanceNumber-t.params.instanceNumber:e.priority()-t.priority()},G.utils.isKeydownButNotEnterEvent=function(e){return"keydown"===e.type&&13!==e.which},G.utils.areElementListsEqual=function(e,t){return e.length===t.length&&-1===_.indexOf(_.map(_.zip(e,t),function(e){return Y(e[0]).is(e[1])}),!1)},G.utils.highlightButton=function(e,t){var n,i="button-see-me",a=!1;function o(){a=!0}return(n=_.extend({delay:0,focusTarget:e},t)).focusTarget.on("focusin",o),setTimeout(function(){n.focusTarget.off("focusin",o),a||(e.addClass(i),e.one("animationend",function(){e.removeClass(i)}))},n.delay),o},G.utils.getCurrentTimestamp=function(){var e=_.now(),t=new Date(G.settings.initialServerDate.replace(/-/g,"/")),e=e-G.settings.initialClientTimestamp;return e+=G.settings.initialClientTimestamp-G.settings.initialServerTimestamp,t.setTime(t.getTime()+e),t.getTime()},G.utils.getRemainingTime=function(e){var e=e instanceof Date?e.getTime():"string"==typeof e?new Date(e.replace(/-/g,"/")).getTime():e,e=e-G.utils.getCurrentTimestamp();return e=Math.ceil(e/1e3)},t=document.createElement("div"),n={transition:"transitionend",OTransition:"oTransitionEnd",MozTransition:"transitionend",WebkitTransition:"webkitTransitionEnd"},i=_.find(_.keys(n),function(e){return!_.isUndefined(t.style[e])}),s=i?n[i]:null,a=G.Class.extend({defaultActiveArguments:{duration:"fast",completeCallback:Y.noop},defaultExpandedArguments:{duration:"fast",completeCallback:Y.noop},containerType:"container",defaults:{title:"",description:"",priority:100,type:"default",content:null,active:!0,instanceNumber:null},initialize:function(e,t){var n=this;n.id=e,a.instanceCounter||(a.instanceCounter=0),a.instanceCounter++,Y.extend(n,{params:_.defaults(t.params||t,n.defaults)}),n.params.instanceNumber||(n.params.instanceNumber=a.instanceCounter),n.notifications=new G.Notifications,n.templateSelector=n.params.templateId||"customize-"+n.containerType+"-"+n.params.type,n.container=Y(n.params.content),0===n.container.length&&(n.container=Y(n.getContainer())),n.headContainer=n.container,n.contentContainer=n.getContent(),n.container=n.container.add(n.contentContainer),n.deferred={embedded:new Y.Deferred},n.priority=new G.Value,n.active=new G.Value,n.activeArgumentsQueue=[],n.expanded=new G.Value,n.expandedArgumentsQueue=[],n.active.bind(function(e){var t=n.activeArgumentsQueue.shift(),t=Y.extend({},n.defaultActiveArguments,t);e=e&&n.isContextuallyActive(),n.onChangeActive(e,t)}),n.expanded.bind(function(e){var t=n.expandedArgumentsQueue.shift(),t=Y.extend({},n.defaultExpandedArguments,t);n.onChangeExpanded(e,t)}),n.deferred.embedded.done(function(){n.setupNotifications(),n.attachEvents()}),G.utils.bubbleChildValueChanges(n,["priority","active"]),n.priority.set(n.params.priority),n.active.set(n.params.active),n.expanded.set(!1)},getNotificationsContainerElement:function(){return this.contentContainer.find(".customize-control-notifications-container:first")},setupNotifications:function(){var e,t=this;t.notifications.container=t.getNotificationsContainerElement(),e=function(){t.expanded.get()&&t.notifications.render()},t.expanded.bind(e),e(),t.notifications.bind("change",_.debounce(e))},ready:function(){},_children:function(t,e){var n=this,i=[];return G[e].each(function(e){e[t].get()===n.id&&i.push(e)}),i.sort(G.utils.prioritySort),i},isContextuallyActive:function(){throw new Error("Container.isContextuallyActive() must be overridden in a subclass.")},onChangeActive:function(e,t){var n,i=this,a=i.headContainer;t.unchanged?t.completeCallback&&t.completeCallback():(n="resolved"===G.previewer.deferred.active.state()?t.duration:0,i.extended(G.Panel)&&(G.panel.each(function(e){e!==i&&e.expanded()&&(n=0)}),e||_.each(i.sections(),function(e){e.collapse({duration:0})})),Y.contains(document,a.get(0))?e?a.slideDown(n,t.completeCallback):i.expanded()?i.collapse({duration:n,completeCallback:function(){a.slideUp(n,t.completeCallback)}}):a.slideUp(n,t.completeCallback):(a.toggle(e),t.completeCallback&&t.completeCallback()))},_toggleActive:function(e,t){return t=t||{},e&&this.active.get()||!e&&!this.active.get()?(t.unchanged=!0,this.onChangeActive(this.active.get(),t),!1):(t.unchanged=!1,this.activeArgumentsQueue.push(t),this.active.set(e),!0)},activate:function(e){return this._toggleActive(!0,e)},deactivate:function(e){return this._toggleActive(!1,e)},onChangeExpanded:function(){throw new Error("Must override with subclass.")},_toggleExpanded:function(e,t){var n,i=this;return n=(t=t||{}).completeCallback,!(e&&!i.active())&&(G.state("paneVisible").set(!0),t.completeCallback=function(){n&&n.apply(i,arguments),e?i.container.trigger("expanded"):i.container.trigger("collapsed")},e&&i.expanded.get()||!e&&!i.expanded.get()?(t.unchanged=!0,i.onChangeExpanded(i.expanded.get(),t),!1):(t.unchanged=!1,i.expandedArgumentsQueue.push(t),i.expanded.set(e),!0))},expand:function(e){return this._toggleExpanded(!0,e)},collapse:function(e){return this._toggleExpanded(!1,e)},_animateChangeExpanded:function(t){var a,o,n,i;s?(o=(a=this).contentContainer,i=o.closest(".wp-full-overlay").add(o),a.panel&&""!==a.panel()&&!G.panel(a.panel()).contentContainer.hasClass("skip-transition")||(i=i.add("#customize-info, .customize-pane-parent")),n=function(e){2===e.eventPhase&&Y(e.target).is(o)&&(o.off(s,n),i.removeClass("busy"),t&&t())},o.on(s,n),i.addClass("busy"),_.defer(function(){var e=o.closest(".wp-full-overlay-sidebar-content"),t=e.scrollTop(),n=o.data("previous-scrollTop")||0,i=a.expanded();i&&0<t?(o.css("top",t+"px"),o.data("previous-scrollTop",t)):!i&&0<t+n&&(o.css("top",n-t+"px"),e.scrollTop(n))})):t&&t()},focus:e,getContainer:function(){var e=this,t=0!==Y("#tmpl-"+e.templateSelector).length?wp.template(e.templateSelector):wp.template("customize-"+e.containerType+"-default");return t&&e.container?t(_.extend({id:e.id},e.params)).toString().trim():"<li></li>"},getContent:function(){var e=this.container,t=e.find(".accordion-section-content, .control-panel-content").first(),n="sub-"+e.attr("id"),i=n,a=e.attr("aria-owns");return e.attr("aria-owns",i=a?i+" "+a:i),t.detach().attr({id:n,class:"customize-pane-child "+t.attr("class")+" "+e.attr("class")})}}),G.Section=a.extend({containerType:"section",containerParent:"#customize-theme-controls",containerPaneParent:".customize-pane-parent",defaults:{title:"",description:"",priority:100,type:"default",content:null,active:!0,instanceNumber:null,panel:null,customizeAction:""},initialize:function(e,t){var n=this,i=t.params||t;i.type||_.find(G.sectionConstructor,function(e,t){return e===n.constructor&&(i.type=t,!0)}),a.prototype.initialize.call(n,e,i),n.id=e,n.panel=new G.Value,n.panel.bind(function(e){Y(n.headContainer).toggleClass("control-subsection",!!e)}),n.panel.set(n.params.panel||""),G.utils.bubbleChildValueChanges(n,["panel"]),n.embed(),n.deferred.embedded.done(function(){n.ready()})},embed:function(){var e,n=this;n.containerParent=G.ensure(n.containerParent),e=function(e){var t;e?G.panel(e,function(e){e.deferred.embedded.done(function(){t=e.contentContainer,n.headContainer.parent().is(t)||t.append(n.headContainer),n.contentContainer.parent().is(n.headContainer)||n.containerParent.append(n.contentContainer),n.deferred.embedded.resolve()})}):(t=G.ensure(n.containerPaneParent),n.headContainer.parent().is(t)||t.append(n.headContainer),n.contentContainer.parent().is(n.headContainer)||n.containerParent.append(n.contentContainer),n.deferred.embedded.resolve())},n.panel.bind(e),e(n.panel.get())},attachEvents:function(){var e,t,n=this;n.container.hasClass("cannot-expand")||(n.container.find(".accordion-section-title, .customize-section-back").on("click keydown",function(e){G.utils.isKeydownButNotEnterEvent(e)||(e.preventDefault(),n.expanded()?n.collapse():n.expand())}),n.container.find(".customize-section-title .customize-help-toggle").on("click",function(){(e=n.container.find(".section-meta")).hasClass("cannot-expand")||((t=e.find(".customize-section-description:first")).toggleClass("open"),t.slideToggle(n.defaultExpandedArguments.duration,function(){t.trigger("toggled")}),Y(this).attr("aria-expanded",function(e,t){return"true"===t?"false":"true"}))}))},isContextuallyActive:function(){var e=this.controls(),t=0;return _(e).each(function(e){e.active()&&(t+=1)}),0!==t},controls:function(){return this._children("section","control")},onChangeExpanded:function(e,t){var n,i,a=this,o=a.headContainer.closest(".wp-full-overlay-sidebar-content"),s=a.contentContainer,r=a.headContainer.closest(".wp-full-overlay"),c=s.find(".customize-section-back"),l=a.headContainer.find(".accordion-section-title").first();e&&!s.hasClass("open")?(n=t.unchanged?t.completeCallback:Y.proxy(function(){a._animateChangeExpanded(function(){l.attr("tabindex","-1"),c.attr("tabindex","0"),c.trigger("focus"),s.css("top",""),o.scrollTop(0),t.completeCallback&&t.completeCallback()}),s.addClass("open"),r.addClass("section-open"),G.state("expandedSection").set(a)},this),t.allowMultiple||G.section.each(function(e){e!==a&&e.collapse({duration:t.duration})}),a.panel()?G.panel(a.panel()).expand({duration:t.duration,completeCallback:n}):(t.allowMultiple||G.panel.each(function(e){e.collapse()}),n())):!e&&s.hasClass("open")?(a.panel()&&(i=G.panel(a.panel())).contentContainer.hasClass("skip-transition")&&i.collapse(),a._animateChangeExpanded(function(){c.attr("tabindex","-1"),l.attr("tabindex","0"),l.trigger("focus"),s.css("top",""),t.completeCallback&&t.completeCallback()}),s.removeClass("open"),r.removeClass("section-open"),a===G.state("expandedSection").get()&&G.state("expandedSection").set(!1)):t.completeCallback&&t.completeCallback()}}),G.ThemesSection=G.Section.extend({currentTheme:"",overlay:"",template:"",screenshotQueue:null,$window:null,$body:null,loaded:0,loading:!1,fullyLoaded:!1,term:"",tags:"",nextTerm:"",nextTags:"",filtersHeight:0,headerContainer:null,updateCountDebounced:null,initialize:function(e,t){var n=this;n.headerContainer=Y(),n.$window=Y(window),n.$body=Y(document.body),G.Section.prototype.initialize.call(n,e,t),n.updateCountDebounced=_.debounce(n.updateCount,500)},embed:function(){var n=this,e=function(e){var t;G.panel(e,function(e){e.deferred.embedded.done(function(){t=e.contentContainer,n.headContainer.parent().is(t)||t.find(".customize-themes-full-container-container").before(n.headContainer),n.contentContainer.parent().is(n.headContainer)||n.containerParent.append(n.contentContainer),n.deferred.embedded.resolve()})})};n.panel.bind(e),e(n.panel.get())},ready:function(){var t=this;t.overlay=t.container.find(".theme-overlay"),t.template=wp.template("customize-themes-details-view"),t.container.on("keydown",function(e){t.overlay.find(".theme-wrap").is(":visible")&&(39===e.keyCode&&t.nextTheme(),37===e.keyCode&&t.previousTheme(),27===e.keyCode&&(t.$body.hasClass("modal-open")?t.closeDetails():t.headerContainer.find(".customize-themes-section-title").focus(),e.stopPropagation()))}),t.renderScreenshots=_.throttle(t.renderScreenshots,100),_.bindAll(t,"renderScreenshots","loadMore","checkTerm","filtersChecked")},isContextuallyActive:function(){return this.active()},attachEvents:function(){var e,n=this;function t(){var e=n.headerContainer.find(".customize-themes-section-title");e.toggleClass("selected",n.expanded()),e.attr("aria-expanded",n.expanded()?"true":"false"),n.expanded()||e.removeClass("details-open")}n.container.find(".customize-section-back").on("click keydown",function(e){G.utils.isKeydownButNotEnterEvent(e)||(e.preventDefault(),n.collapse())}),n.headerContainer=Y("#accordion-section-"+n.id),n.headerContainer.on("click",".customize-themes-section-title",function(){n.headerContainer.find(".filter-details").length&&(n.headerContainer.find(".customize-themes-section-title").toggleClass("details-open").attr("aria-expanded",function(e,t){return"true"===t?"false":"true"}),n.headerContainer.find(".filter-details").slideToggle(180)),n.expanded()||n.expand()}),n.container.on("click",".theme-actions .preview-theme",function(){G.panel("themes").loadThemePreview(Y(this).data("slug"))}),n.container.on("click",".left",function(){n.previousTheme()}),n.container.on("click",".right",function(){n.nextTheme()}),n.container.on("click",".theme-backdrop, .close",function(){n.closeDetails()}),"local"===n.params.filter_type?n.container.on("input",".wp-filter-search-themes",function(e){n.filterSearch(e.currentTarget.value)}):"remote"===n.params.filter_type&&(e=_.debounce(n.checkTerm,500),n.contentContainer.on("input",".wp-filter-search",function(){G.panel("themes").expanded()&&(e(n),n.expanded()||n.expand())}),n.contentContainer.on("click",".filter-group input",function(){n.filtersChecked(),n.checkTerm(n)})),n.contentContainer.on("click",".feature-filter-toggle",function(e){var t=Y(".customize-themes-full-container"),e=Y(e.currentTarget);n.filtersHeight=e.parent().next(".filter-drawer").height(),0<t.scrollTop()&&(t.animate({scrollTop:0},400),e.hasClass("open"))||(e.toggleClass("open").attr("aria-expanded",function(e,t){return"true"===t?"false":"true"}).parent().next(".filter-drawer").slideToggle(180,"linear"),e.hasClass("open")?(e=1018<window.innerWidth?50:76,n.contentContainer.find(".themes").css("margin-top",n.filtersHeight+e)):n.contentContainer.find(".themes").css("margin-top",0))}),n.contentContainer.on("click",".no-themes-local .search-dotorg-themes",function(){G.section("wporg_themes").focus()}),n.expanded.bind(t),t(),G.bind("ready",function(){n.contentContainer=n.container.find(".customize-themes-section"),n.contentContainer.appendTo(Y(".customize-themes-full-container")),n.container.add(n.headerContainer)})},onChangeExpanded:function(e,n){var i=this,t=i.contentContainer.closest(".customize-themes-full-container");function a(){0===i.loaded&&i.loadThemes(),G.section.each(function(e){var t;e!==i&&"themes"===e.params.type&&(t=e.contentContainer.find(".wp-filter-search").val(),i.contentContainer.find(".wp-filter-search").val(t),""===t&&""!==i.term&&"local"!==i.params.filter_type?(i.term="",i.initializeNewQuery(i.term,i.tags)):"remote"===i.params.filter_type?i.checkTerm(i):"local"===i.params.filter_type&&i.filterSearch(t),e.collapse({duration:n.duration}))}),i.contentContainer.addClass("current-section"),t.scrollTop(),t.on("scroll",_.throttle(i.renderScreenshots,300)),t.on("scroll",_.throttle(i.loadMore,300)),n.completeCallback&&n.completeCallback(),i.updateCount()}n.unchanged?n.completeCallback&&n.completeCallback():e?i.panel()&&G.panel.has(i.panel())?G.panel(i.panel()).expand({duration:n.duration,completeCallback:a}):a():(i.contentContainer.removeClass("current-section"),i.headerContainer.find(".filter-details").slideUp(180),t.off("scroll"),n.completeCallback&&n.completeCallback())},getContent:function(){return this.container.find(".control-section-content")},loadThemes:function(){var n,e,i=this;i.loading||(n=Math.ceil(i.loaded/100)+1,e={nonce:G.settings.nonce.switch_themes,wp_customize:"on",theme_action:i.params.action,customized_theme:G.settings.theme.stylesheet,page:n},"remote"===i.params.filter_type&&(e.search=i.term,e.tags=i.tags),i.headContainer.closest(".wp-full-overlay").addClass("loading"),i.loading=!0,i.container.find(".no-themes").hide(),(e=wp.ajax.post("customize_load_themes",e)).done(function(e){var t=e.themes;if(""!==i.nextTerm||""!==i.nextTags)return i.nextTerm&&(i.term=i.nextTerm),i.nextTags&&(i.tags=i.nextTags),i.nextTerm="",i.nextTags="",i.loading=!1,void i.loadThemes();0!==t.length?(i.loadControls(t,n),1===n&&(_.each(i.controls().slice(0,3),function(e){e=e.params.theme.screenshot[0];e&&((new Image).src=e)}),"local"!==i.params.filter_type&&wp.a11y.speak(G.settings.l10n.themeSearchResults.replace("%d",e.info.results))),_.delay(i.renderScreenshots,100),("local"===i.params.filter_type||t.length<100)&&(i.fullyLoaded=!0)):0===i.loaded?(i.container.find(".no-themes").show(),wp.a11y.speak(i.container.find(".no-themes").text())):i.fullyLoaded=!0,"local"===i.params.filter_type?i.updateCount():i.updateCount(e.info.results),i.container.find(".unexpected-error").hide(),i.headContainer.closest(".wp-full-overlay").removeClass("loading"),i.loading=!1}),e.fail(function(e){void 0===e?(i.container.find(".unexpected-error").show(),wp.a11y.speak(i.container.find(".unexpected-error").text())):"undefined"!=typeof console&&console.error&&console.error(e),i.headContainer.closest(".wp-full-overlay").removeClass("loading"),i.loading=!1}))},loadControls:function(e,t){var n=[],i=this;_.each(e,function(e){e=new G.controlConstructor.theme(i.params.action+"_theme_"+e.id,{type:"theme",section:i.params.id,theme:e,priority:i.loaded+1});G.control.add(e),n.push(e),i.loaded=i.loaded+1}),1!==t&&Array.prototype.push.apply(i.screenshotQueue,n)},loadMore:function(){var e,t;this.fullyLoaded||this.loading||(t=(e=this.container.closest(".customize-themes-full-container")).scrollTop()+e.height(),e.prop("scrollHeight")-3e3<t&&this.loadThemes())},filterSearch:function(e){var t,n=0,i=this,a=G.section.has("wporg_themes")&&"remote"!==i.params.filter_type?".no-themes-local":".no-themes",o=i.controls();i.loading||(t=e.toLowerCase().trim().replace(/-/g," ").split(" "),_.each(o,function(e){e.filter(t)&&(n+=1)}),0===n?(i.container.find(a).show(),wp.a11y.speak(i.container.find(a).text())):i.container.find(a).hide(),i.renderScreenshots(),G.reflowPaneContents(),i.updateCountDebounced(n))},checkTerm:function(e){var t;"remote"===e.params.filter_type&&(t=e.contentContainer.find(".wp-filter-search").val(),e.term!==t.trim()&&e.initializeNewQuery(t,e.tags))},filtersChecked:function(){var e=this,t=e.container.find(".filter-group").find(":checkbox"),n=[];_.each(t.filter(":checked"),function(e){n.push(Y(e).prop("value"))}),0===n.length?(n="",e.contentContainer.find(".feature-filter-toggle .filter-count-0").show(),e.contentContainer.find(".feature-filter-toggle .filter-count-filters").hide()):(e.contentContainer.find(".feature-filter-toggle .theme-filter-count").text(n.length),e.contentContainer.find(".feature-filter-toggle .filter-count-0").hide(),e.contentContainer.find(".feature-filter-toggle .filter-count-filters").show()),_.isEqual(e.tags,n)||(e.loading?e.nextTags=n:"remote"===e.params.filter_type?e.initializeNewQuery(e.term,n):"local"===e.params.filter_type&&e.filterSearch(n.join(" ")))},initializeNewQuery:function(e,t){var n=this;_.each(n.controls(),function(e){e.container.remove(),G.control.remove(e.id)}),n.loaded=0,n.fullyLoaded=!1,n.screenshotQueue=null,n.loading?(n.nextTerm=e,n.nextTags=t):(n.term=e,n.tags=t,n.loadThemes()),n.expanded()||n.expand()},renderScreenshots:function(){var s=this;null!==s.screenshotQueue&&0!==s.screenshotQueue.length||(s.screenshotQueue=_.filter(s.controls(),function(e){return!e.screenshotRendered})),s.screenshotQueue.length&&(s.screenshotQueue=_.filter(s.screenshotQueue,function(e){var t=e.container.find(".theme-screenshot"),n=t.find("img");if(!n.length)return!1;if(n.is(":hidden"))return!0;var i=s.$window.scrollTop(),a=i+s.$window.height(),o=n.offset().top,n=t.height(),t=3*n,t=i-t<=o+n&&o<=a+t;return t&&e.container.trigger("render-screenshot"),!t}))},getVisibleCount:function(){return this.contentContainer.find("li.customize-control:visible").length},updateCount:function(e){var t,n;e||0===e||(e=this.getVisibleCount()),n=this.contentContainer.find(".themes-displayed"),t=this.contentContainer.find(".theme-count"),0===e?t.text("0"):(n.fadeOut(180,function(){t.text(e),n.fadeIn(180)}),wp.a11y.speak(G.settings.l10n.announceThemeCount.replace("%d",e)))},nextTheme:function(){var e=this;e.getNextTheme()&&e.showDetails(e.getNextTheme(),function(){e.overlay.find(".right").focus()})},getNextTheme:function(){var e=G.control(this.params.action+"_theme_"+this.currentTheme),t=this.controls(),e=_.indexOf(t,e);return-1!==e&&(!!(e=t[e+1])&&e.params.theme)},previousTheme:function(){var e=this;e.getPreviousTheme()&&e.showDetails(e.getPreviousTheme(),function(){e.overlay.find(".left").focus()})},getPreviousTheme:function(){var e=G.control(this.params.action+"_theme_"+this.currentTheme),t=this.controls(),e=_.indexOf(t,e);return-1!==e&&(!!(e=t[e-1])&&e.params.theme)},updateLimits:function(){this.getNextTheme()||this.overlay.find(".right").addClass("disabled"),this.getPreviousTheme()||this.overlay.find(".left").addClass("disabled")},loadThemePreview:function(e){return G.ThemesPanel.prototype.loadThemePreview.call(this,e)},showDetails:function(e,t){var n=this,i=G.panel("themes");function a(){return!i.canSwitchTheme(e.id)}n.currentTheme=e.id,n.overlay.html(n.template(e)).fadeIn("fast").focus(),n.overlay.find("button.preview, button.preview-theme").toggleClass("disabled",a()),n.overlay.find("button.theme-install").toggleClass("disabled",a()||!1===G.settings.theme._canInstall||!0===G.settings.theme._filesystemCredentialsNeeded),n.$body.addClass("modal-open"),n.containFocus(n.overlay),n.updateLimits(),wp.a11y.speak(G.settings.l10n.announceThemeDetails.replace("%s",e.name)),t&&t()},closeDetails:function(){this.$body.removeClass("modal-open"),this.overlay.fadeOut("fast"),G.control(this.params.action+"_theme_"+this.currentTheme).container.find(".theme").focus()},containFocus:function(t){var n;t.on("keydown",function(e){if(9===e.keyCode)return(n=Y(":tabbable",t)).last()[0]!==e.target||e.shiftKey?n.first()[0]===e.target&&e.shiftKey?(n.last().focus(),!1):void 0:(n.first().focus(),!1)})}}),G.OuterSection=G.Section.extend({initialize:function(){this.containerParent="#customize-outer-theme-controls",this.containerPaneParent=".customize-outer-pane-parent",G.Section.prototype.initialize.apply(this,arguments)},onChangeExpanded:function(e,t){var n,i,a=this,o=a.headContainer.closest(".wp-full-overlay-sidebar-content"),s=a.contentContainer,r=s.find(".customize-section-back"),c=a.headContainer.find(".accordion-section-title").first();Y(document.body).toggleClass("outer-section-open",e),a.container.toggleClass("open",e),a.container.removeClass("busy"),G.section.each(function(e){"outer"===e.params.type&&e.id!==a.id&&e.container.removeClass("open")}),e&&!s.hasClass("open")?(n=t.unchanged?t.completeCallback:Y.proxy(function(){a._animateChangeExpanded(function(){c.attr("tabindex","-1"),r.attr("tabindex","0"),r.focus(),s.css("top",""),o.scrollTop(0),t.completeCallback&&t.completeCallback()}),s.addClass("open")},this),a.panel()?G.panel(a.panel()).expand({duration:t.duration,completeCallback:n}):n()):!e&&s.hasClass("open")?(a.panel()&&(i=G.panel(a.panel())).contentContainer.hasClass("skip-transition")&&i.collapse(),a._animateChangeExpanded(function(){r.attr("tabindex","-1"),c.attr("tabindex","0"),c.trigger("focus"),s.css("top",""),t.completeCallback&&t.completeCallback()}),s.removeClass("open")):t.completeCallback&&t.completeCallback()}}),G.Panel=a.extend({containerType:"panel",initialize:function(e,t){var n=this,i=t.params||t;i.type||_.find(G.panelConstructor,function(e,t){return e===n.constructor&&(i.type=t,!0)}),a.prototype.initialize.call(n,e,i),n.embed(),n.deferred.embedded.done(function(){n.ready()})},embed:function(){var e=this,t=Y("#customize-theme-controls"),n=Y(".customize-pane-parent");e.headContainer.parent().is(n)||n.append(e.headContainer),e.contentContainer.parent().is(e.headContainer)||t.append(e.contentContainer),e.renderContent(),e.deferred.embedded.resolve()},attachEvents:function(){var t,n=this;n.headContainer.find(".accordion-section-title").on("click keydown",function(e){G.utils.isKeydownButNotEnterEvent(e)||(e.preventDefault(),n.expanded()||n.expand())}),n.container.find(".customize-panel-back").on("click keydown",function(e){G.utils.isKeydownButNotEnterEvent(e)||(e.preventDefault(),n.expanded()&&n.collapse())}),(t=n.container.find(".panel-meta:first")).find("> .accordion-section-title .customize-help-toggle").on("click",function(){var e;t.hasClass("cannot-expand")||(e=t.find(".customize-panel-description:first"),t.hasClass("open")?(t.toggleClass("open"),e.slideUp(n.defaultExpandedArguments.duration,function(){e.trigger("toggled")}),Y(this).attr("aria-expanded",!1)):(e.slideDown(n.defaultExpandedArguments.duration,function(){e.trigger("toggled")}),t.toggleClass("open"),Y(this).attr("aria-expanded",!0)))})},sections:function(){return this._children("panel","section")},isContextuallyActive:function(){var e=this.sections(),t=0;return _(e).each(function(e){e.active()&&e.isContextuallyActive()&&(t+=1)}),0!==t},onChangeExpanded:function(e,t){var n,i,a,o,s,r,c;t.unchanged?t.completeCallback&&t.completeCallback():(a=(i=(n=this).contentContainer).closest(".wp-full-overlay"),o=i.closest(".wp-full-overlay-sidebar-content"),s=n.headContainer.find(".accordion-section-title"),r=i.find(".customize-panel-back"),c=n.sections(),e&&!i.hasClass("current-panel")?(G.section.each(function(e){n.id!==e.panel()&&e.collapse({duration:0})}),G.panel.each(function(e){n!==e&&e.collapse({duration:0})}),n.params.autoExpandSoleSection&&1===c.length&&c[0].active.get()?(i.addClass("current-panel skip-transition"),a.addClass("in-sub-panel"),c[0].expand({completeCallback:t.completeCallback})):(n._animateChangeExpanded(function(){s.attr("tabindex","-1"),r.attr("tabindex","0"),r.focus(),i.css("top",""),o.scrollTop(0),t.completeCallback&&t.completeCallback()}),i.addClass("current-panel"),a.addClass("in-sub-panel")),G.state("expandedPanel").set(n)):!e&&i.hasClass("current-panel")&&(i.hasClass("skip-transition")?i.removeClass("skip-transition"):n._animateChangeExpanded(function(){s.attr("tabindex","0"),r.attr("tabindex","-1"),s.focus(),i.css("top",""),t.completeCallback&&t.completeCallback()}),a.removeClass("in-sub-panel"),i.removeClass("current-panel"),n===G.state("expandedPanel").get()&&G.state("expandedPanel").set(!1)))},renderContent:function(){var e=this,t=0!==Y("#tmpl-"+e.templateSelector+"-content").length?wp.template(e.templateSelector+"-content"):wp.template("customize-panel-default-content");t&&e.headContainer&&e.contentContainer.html(t(_.extend({id:e.id},e.params)))}}),G.ThemesPanel=G.Panel.extend({initialize:function(e,t){this.installingThemes=[],G.Panel.prototype.initialize.call(this,e,t)},canSwitchTheme:function(e){return!(!e||e!==G.settings.theme.stylesheet)||"publish"===G.state("selectedChangesetStatus").get()&&(""===G.state("changesetStatus").get()||"auto-draft"===G.state("changesetStatus").get())},attachEvents:function(){var t=this;function e(){t.canSwitchTheme()?t.notifications.remove("theme_switch_unavailable"):t.notifications.add(new G.Notification("theme_switch_unavailable",{message:G.l10n.themePreviewUnavailable,type:"warning"}))}G.Panel.prototype.attachEvents.apply(t),G.settings.theme._canInstall&&G.settings.theme._filesystemCredentialsNeeded&&t.notifications.add(new G.Notification("theme_install_unavailable",{message:G.l10n.themeInstallUnavailable,type:"info",dismissible:!0})),e(),G.state("selectedChangesetStatus").bind(e),G.state("changesetStatus").bind(e),t.contentContainer.on("click",".customize-theme",function(){t.collapse()}),t.contentContainer.on("click",".customize-themes-section-title, .customize-themes-mobile-back",function(){Y(".wp-full-overlay").toggleClass("showing-themes")}),t.contentContainer.on("click",".theme-install",function(e){t.installTheme(e)}),t.contentContainer.on("click",".update-theme, #update-theme",function(e){e.preventDefault(),e.stopPropagation(),t.updateTheme(e)}),t.contentContainer.on("click",".delete-theme",function(e){t.deleteTheme(e)}),_.bindAll(t,"installTheme","updateTheme")},onChangeExpanded:function(e,t){var n,i=!1;G.Panel.prototype.onChangeExpanded.apply(this,[e,t]),t.unchanged?t.completeCallback&&t.completeCallback():(n=this.headContainer.closest(".wp-full-overlay"),e?(n.addClass("in-themes-panel").delay(200).find(".customize-themes-full-container").addClass("animate"),_.delay(function(){n.addClass("themes-panel-expanded")},200),600<window.innerWidth&&(e=this.sections(),_.each(e,function(e){e.expanded()&&(i=!0)}),!i&&0<e.length&&e[0].expand())):n.removeClass("in-themes-panel themes-panel-expanded").find(".customize-themes-full-container").removeClass("animate"))},installTheme:function(e){var t,i=this,a=Y(e.target).data("slug"),o=Y.Deferred(),s=Y(e.target).hasClass("preview");return G.settings.theme._filesystemCredentialsNeeded?o.reject({errorCode:"theme_install_unavailable"}):i.canSwitchTheme(a)?_.contains(i.installingThemes,a)?o.reject({errorCode:"theme_already_installing"}):(wp.updates.maybeRequestFilesystemCredentials(e),t=function(t){var e,n=!1;if(s)G.notifications.remove("theme_installing"),i.loadThemePreview(a);else{if(G.control.each(function(e){"theme"===e.params.type&&e.params.theme.id===t.slug&&(n=e.params.theme,e.rerenderAsInstalled(!0))}),!n||G.control.has("installed_theme_"+n.id))return void o.resolve(t);n.type="installed",e=new G.controlConstructor.theme("installed_theme_"+n.id,{type:"theme",section:"installed_themes",theme:n,priority:0}),G.control.add(e),G.control(e.id).container.trigger("render-screenshot"),G.section.each(function(e){"themes"===e.params.type&&n.id===e.currentTheme&&e.closeDetails()})}o.resolve(t)},i.installingThemes.push(a),e=wp.updates.installTheme({slug:a}),s&&G.notifications.add(new G.OverlayNotification("theme_installing",{message:G.l10n.themeDownloading,type:"info",loading:!0})),e.done(t),e.fail(function(){G.notifications.remove("theme_installing")})):o.reject({errorCode:"theme_switch_unavailable"}),o.promise()},loadThemePreview:function(e){var t,n,i=Y.Deferred();return this.canSwitchTheme(e)?((n=document.createElement("a")).href=location.href,e=_.extend(G.utils.parseQueryString(n.search.substr(1)),{theme:e,changeset_uuid:G.settings.changeset.uuid,return:G.settings.url.return}),G.state("saved").get()||(e.customize_autosaved="on"),n.search=Y.param(e),G.notifications.add(new G.OverlayNotification("theme_previewing",{message:G.l10n.themePreviewWait,type:"info",loading:!0})),t=function(){var e;0<G.state("processing").get()||(G.state("processing").unbind(t),(e=G.requestChangesetUpdate({},{autosave:!0})).done(function(){i.resolve(),Y(window).off("beforeunload.customize-confirm"),location.replace(n.href)}),e.fail(function(){G.notifications.remove("theme_previewing"),i.reject()}))},0===G.state("processing").get()?t():G.state("processing").bind(t)):i.reject({errorCode:"theme_switch_unavailable"}),i.promise()},updateTheme:function(e){wp.updates.maybeRequestFilesystemCredentials(e),Y(document).one("wp-theme-update-success",function(e,t){G.control.each(function(e){"theme"===e.params.type&&e.params.theme.id===t.slug&&(e.params.theme.hasUpdate=!1,e.params.theme.version=t.newVersion,setTimeout(function(){e.rerenderAsInstalled(!0)},2e3))})}),wp.updates.updateTheme({slug:Y(e.target).closest(".notice").data("slug")})},deleteTheme:function(e){var t=Y(e.target).data("slug"),n=G.section("installed_themes");e.preventDefault(),G.settings.theme._filesystemCredentialsNeeded||window.confirm(G.settings.l10n.confirmDeleteTheme)&&(wp.updates.maybeRequestFilesystemCredentials(e),Y(document).one("wp-theme-delete-success",function(){var e=G.control("installed_theme_"+t);e.container.remove(),G.control.remove(e.id),n.loaded=n.loaded-1,n.updateCount(),G.control.each(function(e){"theme"===e.params.type&&e.params.theme.id===t&&e.rerenderAsInstalled(!1)})}),wp.updates.deleteTheme({slug:t}),n.closeDetails(),n.focus())}}),G.Control=G.Class.extend({defaultActiveArguments:{duration:"fast",completeCallback:Y.noop},defaults:{label:"",description:"",active:!0,priority:10},initialize:function(e,t){var n,i=this,a=[];i.params=_.extend({},i.defaults,i.params||{},t.params||t||{}),G.Control.instanceCounter||(G.Control.instanceCounter=0),G.Control.instanceCounter++,i.params.instanceNumber||(i.params.instanceNumber=G.Control.instanceCounter),i.params.type||_.find(G.controlConstructor,function(e,t){return e===i.constructor&&(i.params.type=t,!0)}),i.params.content||(i.params.content=Y("<li></li>",{id:"customize-control-"+e.replace(/]/g,"").replace(/\[/g,"-"),class:"customize-control customize-control-"+i.params.type})),i.id=e,i.selector="#customize-control-"+e.replace(/\]/g,"").replace(/\[/g,"-"),i.params.content?i.container=Y(i.params.content):i.container=Y(i.selector),i.params.templateId?i.templateSelector=i.params.templateId:i.templateSelector="customize-control-"+i.params.type+"-content",i.deferred=_.extend(i.deferred||{},{embedded:new Y.Deferred}),i.section=new G.Value,i.priority=new G.Value,i.active=new G.Value,i.activeArgumentsQueue=[],i.notifications=new G.Notifications({alt:i.altNotice}),i.elements=[],i.active.bind(function(e){var t=i.activeArgumentsQueue.shift(),t=Y.extend({},i.defaultActiveArguments,t);i.onChangeActive(e,t)}),i.section.set(i.params.section),i.priority.set(isNaN(i.params.priority)?10:i.params.priority),i.active.set(i.params.active),G.utils.bubbleChildValueChanges(i,["section","priority","active"]),i.settings={},n={},i.params.setting&&(n.default=i.params.setting),_.extend(n,i.params.settings),_.each(n,function(e,t){var n;_.isObject(e)&&_.isFunction(e.extended)&&e.extended(G.Value)?i.settings[t]=e:_.isString(e)&&((n=G(e))?i.settings[t]=n:a.push(e))}),e=function(){_.each(n,function(e,t){!i.settings[t]&&_.isString(e)&&(i.settings[t]=G(e))}),i.settings[0]&&!i.settings.default&&(i.settings.default=i.settings[0]),i.setting=i.settings.default||null,i.linkElements(),i.embed()},0===a.length?e():G.apply(G,a.concat(e)),i.deferred.embedded.done(function(){i.linkElements(),i.setupNotifications(),i.ready()})},linkElements:function(){var i,a=this,o=a.container.find("[data-customize-setting-link], [data-customize-setting-key-link]"),s={};o.each(function(){var e,t,n=Y(this);if(!n.data("customizeSettingLinked")){if(n.data("customizeSettingLinked",!0),n.is(":radio")){if(e=n.prop("name"),s[e])return;s[e]=!0,n=o.filter('[name="'+e+'"]')}n.data("customizeSettingLink")?t=G(n.data("customizeSettingLink")):n.data("customizeSettingKeyLink")&&(t=a.settings[n.data("customizeSettingKeyLink")]),t&&(i=new G.Element(n),a.elements.push(i),i.sync(t),i.set(t()))}})},embed:function(){var n=this,e=function(e){var t;e&&G.section(e,function(e){e.deferred.embedded.done(function(){t=e.contentContainer.is("ul")?e.contentContainer:e.contentContainer.find("ul:first"),n.container.parent().is(t)||t.append(n.container),n.renderContent(),n.deferred.embedded.resolve()})})};n.section.bind(e),e(n.section.get())},ready:function(){var t,n=this;"dropdown-pages"===n.params.type&&n.params.allow_addition&&((t=n.container.find(".new-content-item")).hide(),n.container.on("click",".add-new-toggle",function(e){Y(e.currentTarget).slideUp(180),t.slideDown(180),t.find(".create-item-input").focus()}),n.container.on("click",".add-content",function(){n.addNewPage()}),n.container.on("keydown",".create-item-input",function(e){13===e.which&&n.addNewPage()}))},getNotificationsContainerElement:function(){var e,t=this,n=t.container.find(".customize-control-notifications-container:first");return n.length||(n=Y('<div class="customize-control-notifications-container"></div>'),t.container.hasClass("customize-control-nav_menu_item")?t.container.find(".menu-item-settings:first").prepend(n):t.container.hasClass("customize-control-widget_form")?t.container.find(".widget-inside:first").prepend(n):(e=t.container.find(".customize-control-title")).length?e.after(n):t.container.prepend(n)),n},setupNotifications:function(){var n,e,i=this;_.each(i.settings,function(n){n.notifications&&(n.notifications.bind("add",function(e){var t=_.extend({},e,{setting:n.id});i.notifications.add(new G.Notification(n.id+":"+e.code,t))}),n.notifications.bind("remove",function(e){i.notifications.remove(n.id+":"+e.code)}))}),n=function(){var e=i.section();(!e||G.section.has(e)&&G.section(e).expanded())&&i.notifications.render()},i.notifications.bind("rendered",function(){var e=i.notifications.get();i.container.toggleClass("has-notifications",0!==e.length),i.container.toggleClass("has-error",0!==_.where(e,{type:"error"}).length)}),i.section.bind(e=function(e,t){t&&G.section.has(t)&&G.section(t).expanded.unbind(n),e&&G.section(e,function(e){e.expanded.bind(n),n()})}),e(i.section.get()),i.notifications.bind("change",_.debounce(n))},renderNotifications:function(){var e,t,n=this,i=!1;"undefined"!=typeof console&&console.warn&&console.warn("[DEPRECATED] wp.customize.Control.prototype.renderNotifications() is deprecated in favor of instantating a wp.customize.Notifications and calling its render() method."),(e=n.getNotificationsContainerElement())&&e.length&&(t=[],n.notifications.each(function(e){t.push(e),"error"===e.type&&(i=!0)}),0===t.length?e.stop().slideUp("fast"):e.stop().slideDown("fast",null,function(){Y(this).css("height","auto")}),n.notificationsTemplate||(n.notificationsTemplate=wp.template("customize-control-notifications")),n.container.toggleClass("has-notifications",0!==t.length),n.container.toggleClass("has-error",i),e.empty().append(Y.trim(n.notificationsTemplate({notifications:t,altNotice:Boolean(n.altNotice)}))))},expand:function(e){G.section(this.section()).expand(e)},focus:e,onChangeActive:function(e,t){t.unchanged?t.completeCallback&&t.completeCallback():Y.contains(document,this.container[0])?e?this.container.slideDown(t.duration,t.completeCallback):this.container.slideUp(t.duration,t.completeCallback):(this.container.toggle(e),t.completeCallback&&t.completeCallback())},toggle:function(e){return this.onChangeActive(e,this.defaultActiveArguments)},activate:a.prototype.activate,deactivate:a.prototype.deactivate,_toggleActive:a.prototype._toggleActive,dropdownInit:function(){function e(e){"string"==typeof e&&i.statuses&&i.statuses[e]?n.html(i.statuses[e]).show():n.hide()}var t=this,n=this.container.find(".dropdown-status"),i=this.params,a=!1;this.container.on("click keydown",".dropdown",function(e){G.utils.isKeydownButNotEnterEvent(e)||(e.preventDefault(),a||t.container.toggleClass("open"),t.container.hasClass("open")&&t.container.parent().parent().find("li.library-selected").focus(),a=!0,setTimeout(function(){a=!1},400))}),this.setting.bind(e),e(this.setting())},renderContent:function(){var e,t=this,n=t.templateSelector;n==="customize-control-"+t.params.type+"-content"&&_.contains(["button","checkbox","date","datetime-local","email","month","number","password","radio","range","search","select","tel","time","text","textarea","week","url"],t.params.type)&&!document.getElementById("tmpl-"+n)&&0===t.container.children().length&&(n="customize-control-default-content"),document.getElementById("tmpl-"+n)&&(e=wp.template(n))&&t.container&&t.container.html(e(t.params)),t.notifications.container=t.getNotificationsContainerElement(),(!(e=t.section())||G.section.has(e)&&G.section(e).expanded())&&t.notifications.render()},addNewPage:function(){var e,a,o,t,s,r,c=this;"dropdown-pages"===c.params.type&&c.params.allow_addition&&G.Menus&&(a=c.container.find(".add-new-toggle"),o=c.container.find(".new-content-item"),t=c.container.find(".create-item-input"),s=t.val(),r=c.container.find("select"),s?(t.removeClass("invalid"),t.attr("disabled","disabled"),(e=G.Menus.insertAutoDraftPost({post_title:s,post_type:"page"})).done(function(e){var t,n,i=new G.Menus.AvailableItemModel({id:"post-"+e.post_id,title:s,type:"post_type",type_label:G.Menus.data.l10n.page_label,object:"page",object_id:e.post_id,url:e.url});G.Menus.availableMenuItemsPanel.collection.add(i),t=Y("#available-menu-items-post_type-page").find(".available-menu-items-list"),n=wp.template("available-menu-item"),t.prepend(n(i.attributes)),r.focus(),c.setting.set(String(e.post_id)),o.slideUp(180),a.slideDown(180)}),e.always(function(){t.val("").removeAttr("disabled")})):t.addClass("invalid"))}}),G.ColorControl=G.Control.extend({ready:function(){var t,n=this,e="hue"===this.params.mode,i=!1;e?(t=this.container.find(".color-picker-hue")).val(n.setting()).wpColorPicker({change:function(e,t){i=!0,n.setting(t.color.h()),i=!1}}):(t=this.container.find(".color-picker-hex")).val(n.setting()).wpColorPicker({change:function(){i=!0,n.setting.set(t.wpColorPicker("color")),i=!1},clear:function(){i=!0,n.setting.set(""),i=!1}}),n.setting.bind(function(e){i||(t.val(e),t.wpColorPicker("color",e))}),n.container.on("keydown",function(e){27===e.which&&n.container.find(".wp-picker-container").hasClass("wp-picker-active")&&(t.wpColorPicker("close"),n.container.find(".wp-color-result").focus(),e.stopPropagation())})}}),G.MediaControl=G.Control.extend({ready:function(){var n=this;function e(e){var t=Y.Deferred();n.extended(G.UploadControl)?t.resolve():(e=parseInt(e,10),_.isNaN(e)||e<=0?(delete n.params.attachment,t.resolve()):n.params.attachment&&n.params.attachment.id===e&&t.resolve()),"pending"===t.state()&&wp.media.attachment(e).fetch().done(function(){n.params.attachment=this.attributes,t.resolve(),wp.customize.previewer.send(n.setting.id+"-attachment-data",this.attributes)}),t.done(function(){n.renderContent()})}_.bindAll(n,"restoreDefault","removeFile","openFrame","select","pausePlayer"),n.container.on("click keydown",".upload-button",n.openFrame),n.container.on("click keydown",".upload-button",n.pausePlayer),n.container.on("click keydown",".thumbnail-image img",n.openFrame),n.container.on("click keydown",".default-button",n.restoreDefault),n.container.on("click keydown",".remove-button",n.pausePlayer),n.container.on("click keydown",".remove-button",n.removeFile),n.container.on("click keydown",".remove-button",n.cleanupPlayer),G.section(n.section()).container.on("expanded",function(){n.player&&n.player.setControlsSize()}).on("collapsed",function(){n.pausePlayer()}),e(n.setting()),n.setting.bind(e)},pausePlayer:function(){this.player&&this.player.pause()},cleanupPlayer:function(){this.player&&wp.media.mixin.removePlayer(this.player)},openFrame:function(e){G.utils.isKeydownButNotEnterEvent(e)||(e.preventDefault(),this.frame||this.initFrame(),this.frame.open())},initFrame:function(){this.frame=wp.media({button:{text:this.params.button_labels.frame_button},states:[new wp.media.controller.Library({title:this.params.button_labels.frame_title,library:wp.media.query({type:this.params.mime_type}),multiple:!1,date:!1})]}),this.frame.on("select",this.select)},select:function(){var e=this.frame.state().get("selection").first().toJSON(),t=window._wpmejsSettings||{};this.params.attachment=e,this.setting(e.id),(e=this.container.find("audio, video").get(0))?this.player=new MediaElementPlayer(e,t):this.cleanupPlayer()},restoreDefault:function(e){G.utils.isKeydownButNotEnterEvent(e)||(e.preventDefault(),this.params.attachment=this.params.defaultAttachment,this.setting(this.params.defaultAttachment.url))},removeFile:function(e){G.utils.isKeydownButNotEnterEvent(e)||(e.preventDefault(),this.params.attachment={},this.setting(""),this.renderContent())}}),G.UploadControl=G.MediaControl.extend({select:function(){var e=this.frame.state().get("selection").first().toJSON(),t=window._wpmejsSettings||{};this.params.attachment=e,this.setting(e.url),(e=this.container.find("audio, video").get(0))?this.player=new MediaElementPlayer(e,t):this.cleanupPlayer()},success:function(){},removerVisibility:function(){}}),G.ImageControl=G.UploadControl.extend({thumbnailSrc:function(){}}),G.BackgroundControl=G.UploadControl.extend({ready:function(){G.UploadControl.prototype.ready.apply(this,arguments)},select:function(){G.UploadControl.prototype.select.apply(this,arguments),wp.ajax.post("custom-background-add",{nonce:_wpCustomizeBackground.nonces.add,wp_customize:"on",customize_theme:G.settings.theme.stylesheet,attachment_id:this.params.attachment.id})}}),G.BackgroundPositionControl=G.Control.extend({ready:function(){var e,n=this;n.container.on("change",'input[name="background-position"]',function(){var e=Y(this).val().split(" ");n.settings.x(e[0]),n.settings.y(e[1])}),e=_.debounce(function(){var e=n.settings.x.get(),t=n.settings.y.get(),t=String(e)+" "+String(t);n.container.find('input[name="background-position"][value="'+t+'"]').trigger("click")}),n.settings.x.bind(e),n.settings.y.bind(e),e()}}),G.CroppedImageControl=G.MediaControl.extend({openFrame:function(e){G.utils.isKeydownButNotEnterEvent(e)||(this.initFrame(),this.frame.setState("library").open())},initFrame:function(){var e=_wpMediaViewsL10n;this.frame=wp.media({button:{text:e.select,close:!1},states:[new wp.media.controller.Library({title:this.params.button_labels.frame_title,library:wp.media.query({type:"image"}),multiple:!1,date:!1,priority:20,suggestedWidth:this.params.width,suggestedHeight:this.params.height}),new wp.media.controller.CustomizeImageCropper({imgSelectOptions:this.calculateImageSelectOptions,control:this})]}),this.frame.on("select",this.onSelect,this),this.frame.on("cropped",this.onCropped,this),this.frame.on("skippedcrop",this.onSkippedCrop,this)},onSelect:function(){var e=this.frame.state().get("selection").first().toJSON();this.params.width!==e.width||this.params.height!==e.height||this.params.flex_width||this.params.flex_height?this.frame.setState("cropper"):(this.setImageFromAttachment(e),this.frame.close())},onCropped:function(e){this.setImageFromAttachment(e)},calculateImageSelectOptions:function(e,t){var n=t.get("control"),i=!!parseInt(n.params.flex_width,10),a=!!parseInt(n.params.flex_height,10),o=e.get("width"),s=e.get("height"),r=parseInt(n.params.width,10),c=parseInt(n.params.height,10),l=r/c,d=r,e=c;return t.set("canSkipCrop",!n.mustBeCropped(i,a,r,c,o,s)),l<o/s?r=(c=s)*l:c=(r=o)/l,!(e={handles:!0,keys:!0,instance:!0,persistent:!0,imageWidth:o,imageHeight:s,minWidth:r<d?r:d,minHeight:c<e?c:e,x1:d=(o-r)/2,y1:e=(s-c)/2,x2:r+d,y2:c+e})==a&&!1==i&&(e.aspectRatio=r+":"+c),!0==a&&(delete e.minHeight,e.maxWidth=o),!0==i&&(delete e.minWidth,e.maxHeight=s),e},mustBeCropped:function(e,t,n,i,a,o){return(!0!==e||!0!==t)&&((!0!==e||i!==o)&&((!0!==t||n!==a)&&((n!==a||i!==o)&&!(a<=n))))},onSkippedCrop:function(){var e=this.frame.state().get("selection").first().toJSON();this.setImageFromAttachment(e)},setImageFromAttachment:function(e){this.params.attachment=e,this.setting(e.id)}}),G.SiteIconControl=G.CroppedImageControl.extend({initFrame:function(){var e=_wpMediaViewsL10n;this.frame=wp.media({button:{text:e.select,close:!1},states:[new wp.media.controller.Library({title:this.params.button_labels.frame_title,library:wp.media.query({type:"image"}),multiple:!1,date:!1,priority:20,suggestedWidth:this.params.width,suggestedHeight:this.params.height}),new wp.media.controller.SiteIconCropper({imgSelectOptions:this.calculateImageSelectOptions,control:this})]}),this.frame.on("select",this.onSelect,this),this.frame.on("cropped",this.onCropped,this),this.frame.on("skippedcrop",this.onSkippedCrop,this)},onSelect:function(){var e=this.frame.state().get("selection").first().toJSON(),t=this;this.params.width!==e.width||this.params.height!==e.height||this.params.flex_width||this.params.flex_height?this.frame.setState("cropper"):wp.ajax.post("crop-image",{nonce:e.nonces.edit,id:e.id,context:"site-icon",cropDetails:{x1:0,y1:0,width:this.params.width,height:this.params.height,dst_width:this.params.width,dst_height:this.params.height}}).done(function(e){t.setImageFromAttachment(e),t.frame.close()}).fail(function(){t.frame.trigger("content:error:crop")})},setImageFromAttachment:function(t){var n;_.each(["site_icon-32","thumbnail","full"],function(e){n||_.isUndefined(t.sizes[e])||(n=t.sizes[e])}),this.params.attachment=t,this.setting(t.id),n&&Y('link[rel="icon"][sizes="32x32"]').attr("href",n.url)},removeFile:function(e){G.utils.isKeydownButNotEnterEvent(e)||(e.preventDefault(),this.params.attachment={},this.setting(""),this.renderContent(),Y('link[rel="icon"][sizes="32x32"]').attr("href","/favicon.ico"))}}),G.HeaderControl=G.Control.extend({ready:function(){this.btnRemove=Y("#customize-control-header_image .actions .remove"),this.btnNew=Y("#customize-control-header_image .actions .new"),_.bindAll(this,"openMedia","removeImage"),this.btnNew.on("click",this.openMedia),this.btnRemove.on("click",this.removeImage),G.HeaderTool.currentHeader=this.getInitialHeaderImage(),new G.HeaderTool.CurrentView({model:G.HeaderTool.currentHeader,el:"#customize-control-header_image .current .container"}),new G.HeaderTool.ChoiceListView({collection:G.HeaderTool.UploadsList=new G.HeaderTool.ChoiceList,el:"#customize-control-header_image .choices .uploaded .list"}),new G.HeaderTool.ChoiceListView({collection:G.HeaderTool.DefaultsList=new G.HeaderTool.DefaultsList,el:"#customize-control-header_image .choices .default .list"}),G.HeaderTool.combinedList=G.HeaderTool.CombinedList=new G.HeaderTool.CombinedList([G.HeaderTool.UploadsList,G.HeaderTool.DefaultsList]),wp.media.controller.Cropper.prototype.defaults.doCropArgs.wp_customize="on",wp.media.controller.Cropper.prototype.defaults.doCropArgs.customize_theme=G.settings.theme.stylesheet},getInitialHeaderImage:function(){if(!G.get().header_image||!G.get().header_image_data||_.contains(["remove-header","random-default-image","random-uploaded-image"],G.get().header_image))return new G.HeaderTool.ImageModel;var e=(e=_.find(_wpCustomizeHeader.uploads,function(e){return e.attachment_id===G.get().header_image_data.attachment_id}))||{url:G.get().header_image,thumbnail_url:G.get().header_image,attachment_id:G.get().header_image_data.attachment_id};return new G.HeaderTool.ImageModel({header:e,choice:e.url.split("/").pop()})},calculateImageSelectOptions:function(e,t){var n=parseInt(_wpCustomizeHeader.data.width,10),i=parseInt(_wpCustomizeHeader.data.height,10),a=!!parseInt(_wpCustomizeHeader.data["flex-width"],10),o=!!parseInt(_wpCustomizeHeader.data["flex-height"],10),s=e.get("width"),e=e.get("height");return this.headerImage=new G.HeaderTool.ImageModel,this.headerImage.set({themeWidth:n,themeHeight:i,themeFlexWidth:a,themeFlexHeight:o,imageWidth:s,imageHeight:e}),t.set("canSkipCrop",!this.headerImage.shouldBeCropped()),(t=n/i)<s/e?n=(i=e)*t:i=(n=s)/t,!(e={handles:!0,keys:!0,instance:!0,persistent:!0,imageWidth:s,imageHeight:e,x1:0,y1:0,x2:n,y2:i})==o&&!1==a&&(e.aspectRatio=n+":"+i),!1==o&&(e.maxHeight=i),!1==a&&(e.maxWidth=n),e},openMedia:function(e){var t=_wpMediaViewsL10n;e.preventDefault(),this.frame=wp.media({button:{text:t.selectAndCrop,close:!1},states:[new wp.media.controller.Library({title:t.chooseImage,library:wp.media.query({type:"image"}),multiple:!1,date:!1,priority:20,suggestedWidth:_wpCustomizeHeader.data.width,suggestedHeight:_wpCustomizeHeader.data.height}),new wp.media.controller.Cropper({imgSelectOptions:this.calculateImageSelectOptions})]}),this.frame.on("select",this.onSelect,this),this.frame.on("cropped",this.onCropped,this),this.frame.on("skippedcrop",this.onSkippedCrop,this),this.frame.open()},onSelect:function(){this.frame.setState("cropper")},onCropped:function(e){var t=e.url,n=e.attachment_id,i=e.width,e=e.height;this.setImageFromURL(t,n,i,e)},onSkippedCrop:function(e){var t=e.get("url"),n=e.get("width"),i=e.get("height");this.setImageFromURL(t,e.id,n,i)},setImageFromURL:function(e,t,n,i){var a={};a.url=e,a.thumbnail_url=e,a.timestamp=_.now(),t&&(a.attachment_id=t),n&&(a.width=n),i&&(a.height=i),e=new G.HeaderTool.ImageModel({header:a,choice:e.split("/").pop()}),G.HeaderTool.UploadsList.add(e),G.HeaderTool.currentHeader.set(e.toJSON()),e.save(),e.importImage()},removeImage:function(){G.HeaderTool.currentHeader.trigger("hide"),G.HeaderTool.CombinedList.trigger("control:removeImage")}}),G.ThemeControl=G.Control.extend({touchDrag:!1,screenshotRendered:!1,ready:function(){var n=this,e=G.panel("themes");function t(){return!e.canSwitchTheme(n.params.theme.id)}function i(){n.container.find("button.preview, button.preview-theme").toggleClass("disabled",t()),n.container.find("button.theme-install").toggleClass("disabled",t()||!1===G.settings.theme._canInstall||!0===G.settings.theme._filesystemCredentialsNeeded)}G.state("selectedChangesetStatus").bind(i),G.state("changesetStatus").bind(i),i(),n.container.on("touchmove",".theme",function(){n.touchDrag=!0}),n.container.on("click keydown touchend",".theme",function(e){var t;if(!G.utils.isKeydownButNotEnterEvent(e))return!0===n.touchDrag?n.touchDrag=!1:void(Y(e.target).is(".theme-actions .button, .update-theme")||(e.preventDefault(),(t=G.section(n.section())).showDetails(n.params.theme,function(){G.settings.theme._filesystemCredentialsNeeded&&t.overlay.find(".theme-actions .delete-theme").remove()})))}),n.container.on("render-screenshot",function(){var e=Y(this).find("img"),t=e.data("src");t&&e.attr("src",t),n.screenshotRendered=!0})},filter:function(e){var t=this,n=0,i=(i=t.params.theme.name+" "+t.params.theme.description+" "+t.params.theme.tags+" "+t.params.theme.author+" ").toLowerCase().replace("-"," ");return _.isArray(e)||(e=[e]),t.params.theme.name.toLowerCase()===e.join(" ")?n=100:(n+=10*(i.split(e.join(" ")).length-1),_.each(e,function(e){n=(n+=2*(i.split(e+" ").length-1))+i.split(e).length-1}),99<n&&(n=99)),0!==n?(t.activate(),t.params.priority=101-n,!0):(t.deactivate(),!(t.params.priority=101))},rerenderAsInstalled:function(e){var t=this;e?t.params.theme.type="installed":(e=G.section(t.params.section),t.params.theme.type=e.params.action),t.renderContent(),t.container.trigger("render-screenshot")}}),G.CodeEditorControl=G.Control.extend({initialize:function(e,t){var n=this;n.deferred=_.extend(n.deferred||{},{codemirror:Y.Deferred()}),G.Control.prototype.initialize.call(n,e,t),n.notifications.bind("add",function(e){var t;e.code===n.setting.id+":csslint_error"&&(e.templateId="customize-code-editor-lint-error-notification",e.render=(t=e.render,function(){var e=t.call(this);return e.find("input[type=checkbox]").on("click",function(){n.setting.notifications.remove("csslint_error")}),e}))})},ready:function(){var i=this;i.section()?G.section(i.section(),function(n){n.deferred.embedded.done(function(){var t;n.expanded()?i.initEditor():(t=function(e){e&&(i.initEditor(),n.expanded.unbind(t))},n.expanded.bind(t))})}):i.initEditor()},initEditor:function(){var e,t=this,n=!1;wp.codeEditor&&(_.isUndefined(t.params.editor_settings)||!1!==t.params.editor_settings)&&((n=wp.codeEditor.defaultSettings?_.clone(wp.codeEditor.defaultSettings):{}).codemirror=_.extend({},n.codemirror,{indentUnit:2,tabSize:2}),_.isObject(t.params.editor_settings)&&_.each(t.params.editor_settings,function(e,t){_.isObject(e)&&(n[t]=_.extend({},n[t],e))})),e=new G.Element(t.container.find("textarea")),t.elements.push(e),e.sync(t.setting),e.set(t.setting()),n?t.initSyntaxHighlightingEditor(n):t.initPlainTextareaEditor()},focus:function(e){var t=this,e=_.extend({},e),n=e.completeCallback;e.completeCallback=function(){n&&n(),t.editor&&t.editor.codemirror.focus()},G.Control.prototype.focus.call(t,e)},initSyntaxHighlightingEditor:function(e){var t=this,n=t.container.find("textarea"),i=!1,e=_.extend({},e,{onTabNext:_.bind(t.onTabNext,t),onTabPrevious:_.bind(t.onTabPrevious,t),onUpdateErrorNotice:_.bind(t.onUpdateErrorNotice,t)});t.editor=wp.codeEditor.initialize(n,e),Y(t.editor.codemirror.display.lineDiv).attr({role:"textbox","aria-multiline":"true","aria-label":t.params.label,"aria-describedby":"editor-keyboard-trap-help-1 editor-keyboard-trap-help-2 editor-keyboard-trap-help-3 editor-keyboard-trap-help-4"}),t.container.find("label").on("click",function(){t.editor.codemirror.focus()}),t.editor.codemirror.on("change",function(e){i=!0,n.val(e.getValue()).trigger("change"),i=!1}),t.setting.bind(function(e){i||t.editor.codemirror.setValue(e)}),t.editor.codemirror.on("keydown",function(e,t){27===t.keyCode&&t.stopPropagation()}),t.deferred.codemirror.resolveWith(t,[t.editor.codemirror])},onTabNext:function(){var e=G.section(this.section()).controls(),t=e.indexOf(this);e.length===t+1?Y("#customize-footer-actions .collapse-sidebar").trigger("focus"):e[t+1].container.find(":focusable:first").focus()},onTabPrevious:function(){var e=G.section(this.section()),t=e.controls(),n=t.indexOf(this);(0===n?e.contentContainer.find(".customize-section-title .customize-help-toggle, .customize-section-title .customize-section-description.open .section-description-close").last():t[n-1].contentContainer.find(":focusable:first")).focus()},onUpdateErrorNotice:function(e){this.setting.notifications.remove("csslint_error"),0!==e.length&&(e=1===e.length?G.l10n.customCssError.singular.replace("%d","1"):G.l10n.customCssError.plural.replace("%d",String(e.length)),this.setting.notifications.add(new G.Notification("csslint_error",{message:e,type:"error"})))},initPlainTextareaEditor:function(){var a=this.container.find("textarea"),o=a[0];a.on("blur",function(){a.data("next-tab-blurs",!1)}),a.on("keydown",function(e){var t,n,i;27!==e.keyCode?9!==e.keyCode||e.ctrlKey||e.altKey||e.shiftKey||a.data("next-tab-blurs")||(t=o.selectionStart,n=o.selectionEnd,i=o.value,0<=t&&(o.value=i.substring(0,t).concat("\t",i.substring(n)),a.selectionStart=o.selectionEnd=t+1),e.stopPropagation(),e.preventDefault()):a.data("next-tab-blurs")||(a.data("next-tab-blurs",!0),e.stopPropagation())}),this.deferred.codemirror.rejectWith(this)}}),G.DateTimeControl=G.Control.extend({ready:function(){var i=this;if(i.inputElements={},i.invalidDate=!1,_.bindAll(i,"populateSetting","updateDaysForMonth","populateDateInputs"),!i.setting)throw new Error("Missing setting");i.container.find(".date-input").each(function(){var e=Y(this),t=e.data("component"),n=new G.Element(e);i.inputElements[t]=n,i.elements.push(n),e.on("change",function(){i.invalidDate&&i.notifications.add(new G.Notification("invalid_date",{message:G.l10n.invalidDate}))}),e.on("input",_.debounce(function(){i.invalidDate||i.notifications.remove("invalid_date")})),e.on("blur",_.debounce(function(){i.invalidDate||i.populateDateInputs()}))}),i.inputElements.month.bind(i.updateDaysForMonth),i.inputElements.year.bind(i.updateDaysForMonth),i.populateDateInputs(),i.setting.bind(i.populateDateInputs),_.each(i.inputElements,function(e){e.bind(i.populateSetting)})},parseDateTime:function(e){var t;return(t=e?e.match(/^(\d\d\d\d)-(\d\d)-(\d\d)(?: (\d\d):(\d\d)(?::(\d\d))?)?$/):t)?(t.shift(),t={year:t.shift(),month:t.shift(),day:t.shift(),hour:t.shift()||"00",minute:t.shift()||"00",second:t.shift()||"00"},this.params.includeTime&&this.params.twelveHourFormat&&(t.hour=parseInt(t.hour,10),t.meridian=12<=t.hour?"pm":"am",t.hour=t.hour%12?String(t.hour%12):String(12),delete t.second),t):null},validateInputs:function(){var e,i,a=this;return a.invalidDate=!1,e=["year","day"],a.params.includeTime&&e.push("hour","minute"),_.find(e,function(e){var t,n=a.inputElements[e];return i=n.element.get(0),t=parseInt(n.element.attr("max"),10),e=parseInt(n.element.attr("min"),10),n=parseInt(n(),10),a.invalidDate=isNaN(n)||t<n||n<e,a.invalidDate||i.setCustomValidity(""),a.invalidDate}),a.inputElements.meridian&&!a.invalidDate&&(i=a.inputElements.meridian.element.get(0),"am"!==a.inputElements.meridian.get()&&"pm"!==a.inputElements.meridian.get()?a.invalidDate=!0:i.setCustomValidity("")),a.invalidDate?i.setCustomValidity(G.l10n.invalidValue):i.setCustomValidity(""),(!a.section()||G.section.has(a.section())&&G.section(a.section()).expanded())&&_.result(i,"reportValidity"),a.invalidDate},updateDaysForMonth:function(){var e=this,t=parseInt(e.inputElements.month(),10),n=parseInt(e.inputElements.year(),10),i=parseInt(e.inputElements.day(),10);t&&n&&(t=new Date(n,t,0).getDate(),e.inputElements.day.element.attr("max",t),t<i&&e.inputElements.day(String(t)))},populateSetting:function(){var e,t=this;return!(t.validateInputs()||!t.params.allowPastDate&&!t.isFutureDate())&&(e=t.convertInputDateToString(),t.setting.set(e),!0)},convertInputDateToString:function(){var e,n=this,t="",i=function(e,t){return String(e).length<t&&(t=t-String(e).length,e=Math.pow(10,t).toString().substr(1)+String(e)),e},a=function(e){var t=parseInt(n.inputElements[e].get(),10);return _.contains(["month","day","hour","minute"],e)?t=i(t,2):"year"===e&&(t=i(t,4)),t},o=["year","-","month","-","day"];return n.params.includeTime&&(e=n.inputElements.meridian?n.convertHourToTwentyFourHourFormat(n.inputElements.hour(),n.inputElements.meridian()):n.inputElements.hour(),o=o.concat([" ",i(e,2),":","minute",":","00"])),_.each(o,function(e){t+=n.inputElements[e]?a(e):e}),t},isFutureDate:function(){return 0<G.utils.getRemainingTime(this.convertInputDateToString())},convertHourToTwentyFourHourFormat:function(e,t){var e=parseInt(e,10);return isNaN(e)?"":(e="pm"===t&&e<12?e+12:"am"===t&&12===e?e-12:e,String(e))},populateDateInputs:function(){var i=this.parseDateTime(this.setting.get());return!!i&&(_.each(this.inputElements,function(e,t){var n=i[t];"month"===t||"meridian"===t?(n=n.replace(/^0/,""),e.set(n)):(n=parseInt(n,10),e.element.is(document.activeElement)?n!==parseInt(e(),10)&&e.set(String(n)):e.set(i[t]))}),!0)},toggleFutureDateNotification:function(e){var t="not_future_date";return e?(e=new G.Notification(t,{type:"error",message:G.l10n.futureDateError}),this.notifications.add(e)):this.notifications.remove(t),this}}),G.PreviewLinkControl=G.Control.extend({defaults:_.extend({},G.Control.prototype.defaults,{templateId:"customize-preview-link-control"}),ready:function(){var e,t,n,i,a,o=this;_.bindAll(o,"updatePreviewLink"),o.setting||(o.setting=new G.Value),o.previewElements={},o.container.find(".preview-control-element").each(function(){t=Y(this),e=t.data("component"),t=new G.Element(t),o.previewElements[e]=t,o.elements.push(t)}),n=o.previewElements.url,i=o.previewElements.input,a=o.previewElements.button,i.link(o.setting),n.link(o.setting),n.bind(function(e){n.element.parent().attr({href:e,target:G.settings.changeset.uuid})}),G.bind("ready",o.updatePreviewLink),G.state("saved").bind(o.updatePreviewLink),G.state("changesetStatus").bind(o.updatePreviewLink),G.state("activated").bind(o.updatePreviewLink),G.previewer.previewUrl.bind(o.updatePreviewLink),a.element.on("click",function(e){e.preventDefault(),o.setting()&&(i.element.select(),document.execCommand("copy"),a(a.element.data("copied-text")))}),n.element.parent().on("click",function(e){Y(this).hasClass("disabled")&&e.preventDefault()}),a.element.on("mouseenter",function(){o.setting()&&a(a.element.data("copy-text"))})},updatePreviewLink:function(){var e=!G.state("saved").get()||""===G.state("changesetStatus").get()||"auto-draft"===G.state("changesetStatus").get();this.toggleSaveNotification(e),this.previewElements.url.element.parent().toggleClass("disabled",e),this.previewElements.button.element.prop("disabled",e),this.setting.set(G.previewer.getFrontendPreviewUrl())},toggleSaveNotification:function(e){var t="changes_not_saved";e?(e=new G.Notification(t,{type:"info",message:G.l10n.saveBeforeShare}),this.notifications.add(e)):this.notifications.remove(t)}}),G.defaultConstructor=G.Setting,G.control=new G.Values({defaultConstructor:G.Control}),G.section=new G.Values({defaultConstructor:G.Section}),G.panel=new G.Values({defaultConstructor:G.Panel}),G.notifications=new G.Notifications,G.PreviewFrame=G.Messenger.extend({sensitivity:null,initialize:function(e,t){var n=Y.Deferred();n.promise(this),this.container=e.container,Y.extend(e,{channel:G.PreviewFrame.uuid()}),G.Messenger.prototype.initialize.call(this,e,t),this.add("previewUrl",e.previewUrl),this.query=Y.extend(e.query||{},{customize_messenger_channel:this.channel()}),this.run(n)},run:function(t){var e,n,i,a=this,o=!1,s=!1,r=null,c="{}"!==a.query.customized;a._ready&&a.unbind("ready",a._ready),a._ready=function(e){s=!0,r=e,a.container.addClass("iframe-ready"),e&&o&&t.resolveWith(a,[e])},a.bind("ready",a._ready),(e=document.createElement("a")).href=a.previewUrl(),n=_.extend(G.utils.parseQueryString(e.search.substr(1)),{customize_changeset_uuid:a.query.customize_changeset_uuid,customize_theme:a.query.customize_theme,customize_messenger_channel:a.query.customize_messenger_channel}),!G.settings.changeset.autosaved&&G.state("saved").get()||(n.customize_autosaved="on"),e.search=Y.param(n),a.iframe=Y("<iframe />",{title:G.l10n.previewIframeTitle,name:"customize-"+a.channel()}),a.iframe.attr("onmousewheel",""),a.iframe.attr("sandbox","allow-forms allow-modals allow-orientation-lock allow-pointer-lock allow-popups allow-popups-to-escape-sandbox allow-presentation allow-same-origin allow-scripts"),c?a.iframe.attr("data-src",e.href):a.iframe.attr("src",e.href),a.iframe.appendTo(a.container),a.targetWindow(a.iframe[0].contentWindow),c&&((i=Y("<form>",{action:e.href,target:a.iframe.attr("name"),method:"post",hidden:"hidden"})).append(Y("<input>",{type:"hidden",name:"_method",value:"GET"})),_.each(a.query,function(e,t){i.append(Y("<input>",{type:"hidden",name:t,value:e}))}),a.container.append(i),i.trigger("submit"),i.remove()),a.bind("iframe-loading-error",function(e){a.iframe.remove(),0!==e?-1!==e?t.rejectWith(a,["request failure"]):t.rejectWith(a,["cheatin"]):a.login(t)}),a.iframe.one("load",function(){o=!0,s?t.resolveWith(a,[r]):setTimeout(function(){t.rejectWith(a,["ready timeout"])},a.sensitivity)})},login:function(n){var i=this,a=function(){n.rejectWith(i,["logged out"])};if(this.triedLogin)return a();Y.get(G.settings.url.ajax,{action:"logged-in"}).fail(a).done(function(e){var t;"1"!==e&&a(),(t=Y("<iframe />",{src:i.previewUrl(),title:G.l10n.previewIframeTitle}).hide()).appendTo(i.container),t.on("load",function(){i.triedLogin=!0,t.remove(),i.run(n)})})},destroy:function(){G.Messenger.prototype.destroy.call(this),this.iframe&&this.iframe.remove(),delete this.iframe,delete this.targetWindow}}),o=0,G.PreviewFrame.uuid=function(){return"preview-"+String(o++)},G.setDocumentTitle=function(e){e=G.settings.documentTitleTmpl.replace("%s",e);document.title=e,G.trigger("title",e)},G.Previewer=G.Messenger.extend({refreshBuffer:null,initialize:function(e,t){var n,o=this,i=document.createElement("a");Y.extend(o,t||{}),o.deferred={active:Y.Deferred()},o.refresh=_.debounce((n=o.refresh,function(){var e,t=function(){return 0===G.state("processing").get()};t()?n.call(o):(e=function(){t()&&(n.call(o),G.state("processing").unbind(e))},G.state("processing").bind(e))}),o.refreshBuffer),o.container=G.ensure(e.container),o.allowedUrls=e.allowedUrls,e.url=window.location.href,G.Messenger.prototype.initialize.call(o,e),i.href=o.origin(),o.add("scheme",i.protocol.replace(/:$/,"")),o.add("previewUrl",e.previewUrl).setter(function(e){var n,i=null,t=[],a=document.createElement("a");return a.href=e,/\/wp-(admin|includes|content)(\/|$)/.test(a.pathname)?null:(1<a.search.length&&(delete(e=G.utils.parseQueryString(a.search.substr(1))).customize_changeset_uuid,delete e.customize_theme,delete e.customize_messenger_channel,delete e.customize_autosaved,_.isEmpty(e)?a.search="":a.search=Y.param(e)),t.push(a),o.scheme.get()+":"!==a.protocol&&((a=document.createElement("a")).href=t[0].href,a.protocol=o.scheme.get()+":",t.unshift(a)),n=document.createElement("a"),_.find(t,function(t){return!_.isUndefined(_.find(o.allowedUrls,function(e){if(n.href=e,a.protocol===n.protocol&&a.host===n.host&&0===a.pathname.indexOf(n.pathname.replace(/\/$/,"")))return i=t.href,!0}))}),i)}),o.bind("ready",o.ready),o.deferred.active.done(_.bind(o.keepPreviewAlive,o)),o.bind("synced",function(){o.send("active")}),o.previewUrl.bind(o.refresh),o.scroll=0,o.bind("scroll",function(e){o.scroll=e}),o.bind("url",function(e){var t,n=!1;o.scroll=0,o.previewUrl.bind(t=function(){n=!0}),o.previewUrl.set(e),o.previewUrl.unbind(t),n||o.refresh()}),o.bind("documentTitle",function(e){G.setDocumentTitle(e)})},ready:function(e){var t=this,n={};n.settings=G.get(),n["settings-modified-while-loading"]=t.settingsModifiedWhileLoading,"resolved"===t.deferred.active.state()&&!t.loading||(n.scroll=t.scroll),n["edit-shortcut-visibility"]=G.state("editShortcutVisibility").get(),t.send("sync",n),e.currentUrl&&(t.previewUrl.unbind(t.refresh),t.previewUrl.set(e.currentUrl),t.previewUrl.bind(t.refresh)),t={panel:e.activePanels,section:e.activeSections,control:e.activeControls},_(t).each(function(n,i){G[i].each(function(e,t){_.isUndefined(G.settings[i+"s"][t])&&_.isUndefined(n[t])||(n[t]?e.activate():e.deactivate())})}),e.settingValidities&&G._handleSettingValidities({settingValidities:e.settingValidities,focusInvalidControl:!1})},keepPreviewAlive:function(){var e,t=function(){e=setTimeout(i,G.settings.timeouts.keepAliveCheck)},n=function(){G.state("previewerAlive").set(!0),clearTimeout(e),t()},i=function(){G.state("previewerAlive").set(!1)};t(),this.bind("ready",n),this.bind("keep-alive",n)},query:function(){},abort:function(){this.loading&&(this.loading.destroy(),delete this.loading)},refresh:function(){var e,i=this;i.send("loading-initiated"),i.abort(),i.loading=new G.PreviewFrame({url:i.url(),previewUrl:i.previewUrl(),query:i.query({excludeCustomizedSaved:!0})||{},container:i.container}),i.settingsModifiedWhileLoading={},G.bind("change",e=function(e){i.settingsModifiedWhileLoading[e.id]=!0}),i.loading.always(function(){G.unbind("change",e)}),i.loading.done(function(e){var t,n=this;i.preview=n,i.targetWindow(n.targetWindow()),i.channel(n.channel()),t=function(){n.unbind("synced",t),i._previousPreview&&i._previousPreview.destroy(),i._previousPreview=i.preview,i.deferred.active.resolve(),delete i.loading},n.bind("synced",t),i.trigger("ready",e)}),i.loading.fail(function(e){i.send("loading-failed"),"logged out"===e&&(i.preview&&(i.preview.destroy(),delete i.preview),i.login().done(i.refresh)),"cheatin"===e&&i.cheatin()})},login:function(){var t,n,i,a=this;return this._login||(t=Y.Deferred(),this._login=t.promise(),n=new G.Messenger({channel:"login",url:G.settings.url.login}),i=Y("<iframe />",{src:G.settings.url.login,title:G.l10n.loginIframeTitle}).appendTo(this.container),n.targetWindow(i[0].contentWindow),n.bind("login",function(){var e=a.refreshNonces();e.always(function(){i.remove(),n.destroy(),delete a._login}),e.done(function(){t.resolve()}),e.fail(function(){a.cheatin(),t.reject()})}),this._login)},cheatin:function(){Y(document.body).empty().addClass("cheatin").append("<h1>"+G.l10n.notAllowedHeading+"</h1><p>"+G.l10n.notAllowed+"</p>")},refreshNonces:function(){var e,t=Y.Deferred();return t.promise(),(e=wp.ajax.post("customize_refresh_nonces",{wp_customize:"on",customize_theme:G.settings.theme.stylesheet})).done(function(e){G.trigger("nonce-refresh",e),t.resolve()}),e.fail(function(){t.reject()}),t}}),G.settingConstructor={},G.controlConstructor={color:G.ColorControl,media:G.MediaControl,upload:G.UploadControl,image:G.ImageControl,cropped_image:G.CroppedImageControl,site_icon:G.SiteIconControl,header:G.HeaderControl,background:G.BackgroundControl,background_position:G.BackgroundPositionControl,theme:G.ThemeControl,date_time:G.DateTimeControl,code_editor:G.CodeEditorControl},G.panelConstructor={themes:G.ThemesPanel},G.sectionConstructor={themes:G.ThemesSection,outer:G.OuterSection},G._handleSettingValidities=function(e){var o=[],n=!1;_.each(e.settingValidities,function(t,e){var a=G(e);a&&(_.isObject(t)&&_.each(t,function(e,t){var n=!1,i=new G.Notification(t,_.extend({fromServer:!0},e)),e=a.notifications(i.code);(n=e?i.type!==e.type||i.message!==e.message||!_.isEqual(i.data,e.data):n)&&a.notifications.remove(t),a.notifications.has(i.code)||a.notifications.add(i),o.push(a.id)}),a.notifications.each(function(e){!e.fromServer||"error"!==e.type||!0!==t&&t[e.code]||a.notifications.remove(e.code)}))}),e.focusInvalidControl&&(e=G.findControlsForSettings(o),_(_.values(e)).find(function(e){return _(e).find(function(e){var t=e.section()&&G.section.has(e.section())&&G.section(e.section()).expanded();return(t=t&&e.expanded?e.expanded():t)&&(e.focus(),n=!0),n})}),n||_.isEmpty(e)||_.values(e)[0][0].focus())},G.findControlsForSettings=function(e){var n,i={};return _.each(_.unique(e),function(e){var t=G(e);t&&(n=t.findControls())&&0<n.length&&(i[e]=n)}),i},G.reflowPaneContents=_.bind(function(){var i,e,t,a=[],o=!1;document.activeElement&&(e=Y(document.activeElement)),G.panel.each(function(e){var t,n;"themes"!==e.id&&(t=e.sections(),n=_.pluck(t,"headContainer"),a.push(e),i=e.contentContainer.is("ul")?e.contentContainer:e.contentContainer.find("ul:first"),G.utils.areElementListsEqual(n,i.children("[id]"))||(_(t).each(function(e){i.append(e.headContainer)}),o=!0))}),G.section.each(function(e){var t=e.controls(),n=_.pluck(t,"container");e.panel()||a.push(e),i=e.contentContainer.is("ul")?e.contentContainer:e.contentContainer.find("ul:first"),G.utils.areElementListsEqual(n,i.children("[id]"))||(_(t).each(function(e){i.append(e.container)}),o=!0)}),a.sort(G.utils.prioritySort),t=_.pluck(a,"headContainer"),i=Y("#customize-theme-controls .customize-pane-parent"),G.utils.areElementListsEqual(t,i.children())||(_(a).each(function(e){i.append(e.headContainer)}),o=!0),G.panel.each(function(e){var t=e.active();e.active.callbacks.fireWith(e.active,[t,t])}),G.section.each(function(e){var t=e.active();e.active.callbacks.fireWith(e.active,[t,t])}),o&&e&&e.trigger("focus"),G.trigger("pane-contents-reflowed")},G),G.state=new G.Values,_.each(["saved","saving","trashing","activated","processing","paneVisible","expandedPanel","expandedSection","changesetDate","selectedChangesetDate","changesetStatus","selectedChangesetStatus","remainingTimeToPublish","previewerAlive","editShortcutVisibility","changesetLocked","previewedDevice"],function(e){G.state.create(e)}),Y(function(){var h,o,t,n,i,d,u,p,a,s,r,e,c,l,f,m,g,v,w,b,C,y,x,k,z,S,T,E,D,P,N,I,U,A,F,V,H,L,M,O,j,R,B;function W(e){e&&e.lockUser&&(G.settings.changeset.lockUser=e.lockUser),G.state("changesetLocked").set(!0),G.notifications.add(new T("changeset_locked",{lockUser:G.settings.changeset.lockUser,allowOverride:Boolean(e&&e.allowOverride)}))}function q(){var e,t=document.createElement("a");return t.href=location.href,e=G.utils.parseQueryString(t.search.substr(1)),G.settings.changeset.latestAutoDraftUuid?e.changeset_uuid=G.settings.changeset.latestAutoDraftUuid:e.customize_autosaved="on",e.return=G.settings.url.return,t.search=Y.param(e),t.href}function Q(){U||(wp.ajax.post("customize_dismiss_autosave_or_lock",{wp_customize:"on",customize_theme:G.settings.theme.stylesheet,customize_changeset_uuid:G.settings.changeset.uuid,nonce:G.settings.nonce.dismiss_autosave_or_lock,dismiss_autosave:!0}),U=!0)}function K(){var e;return G.state("activated").get()?(""!==(e=G.state("changesetStatus").get())&&"auto-draft"!==e||(e="publish"),G.state("selectedChangesetStatus").get()===e&&(("future"!==G.state("selectedChangesetStatus").get()||G.state("selectedChangesetDate").get()===G.state("changesetDate").get())&&(G.state("saved").get()&&"auto-draft"!==G.state("changesetStatus").get()))):0===G._latestRevision}function $(){G.unbind("change",$),G.state("selectedChangesetStatus").unbind($),G.state("selectedChangesetDate").unbind($),Y(window).on("beforeunload.customize-confirm",function(){if(!K()&&!G.state("changesetLocked").get())return setTimeout(function(){t.removeClass("customize-loading")},1),G.l10n.saveAlert})}function J(){var e=Y.Deferred(),t=!1,n=!1;return K()?n=!0:confirm(G.l10n.saveAlert)?(n=!0,G.each(function(e){e._dirty=!1}),Y(document).off("visibilitychange.wp-customize-changeset-update"),Y(window).off("beforeunload.wp-customize-changeset-update"),i.css("cursor","progress"),""!==G.state("changesetStatus").get()&&(t=!0)):e.reject(),(n||t)&&wp.ajax.send("customize_dismiss_autosave_or_lock",{timeout:500,data:{wp_customize:"on",customize_theme:G.settings.theme.stylesheet,customize_changeset_uuid:G.settings.changeset.uuid,nonce:G.settings.nonce.dismiss_autosave_or_lock,dismiss_autosave:t,dismiss_lock:n}}).always(function(){e.resolve()}),e.promise()}G.settings=window._wpCustomizeSettings,G.l10n=window._wpCustomizeControlsL10n,G.settings&&(!Y.support.postMessage||!Y.support.cors&&G.settings.isCrossDomain||(null===G.PreviewFrame.prototype.sensitivity&&(G.PreviewFrame.prototype.sensitivity=G.settings.timeouts.previewFrameSensitivity),null===G.Previewer.prototype.refreshBuffer&&(G.Previewer.prototype.refreshBuffer=G.settings.timeouts.windowRefresh),o=Y(document.body),t=o.children(".wp-full-overlay"),n=Y("#customize-info .panel-title.site-title"),i=Y(".customize-controls-close"),d=Y("#save"),u=Y("#customize-save-button-wrapper"),p=Y("#publish-settings"),a=Y("#customize-footer-actions"),G.bind("ready",function(){G.section.add(new G.OuterSection("publish_settings",{title:G.l10n.publishSettings,priority:0,active:G.settings.theme.active}))}),G.section("publish_settings",function(t){var e,n,i,a,o,s,r;function c(){r=r||G.utils.highlightButton(u,{delay:1e3,focusTarget:d})}function l(){r&&(r(),r=null)}e=new G.Control("trash_changeset",{type:"button",section:t.id,priority:30,input_attrs:{class:"button-link button-link-delete",value:G.l10n.discardChanges}}),G.control.add(e),e.deferred.embedded.done(function(){e.container.find(".button-link").on("click",function(){confirm(G.l10n.trashConfirm)&&wp.customize.previewer.trash()})}),G.control.add(new G.PreviewLinkControl("changeset_preview_link",{section:t.id,priority:100})),t.active.validate=n=function(){return!!G.state("activated").get()&&(!G.state("trashing").get()&&"trash"!==G.state("changesetStatus").get()&&(""!==G.state("changesetStatus").get()||!G.state("saved").get()))},s=function(){t.active.set(n())},G.state("activated").bind(s),G.state("trashing").bind(s),G.state("saved").bind(s),G.state("changesetStatus").bind(s),s(),(s=function(){p.toggle(t.active.get()),d.toggleClass("has-next-sibling",t.active.get())})(),t.active.bind(s),G.state("selectedChangesetStatus").bind(l),t.contentContainer.find(".customize-action").text(G.l10n.updating),t.contentContainer.find(".customize-section-back").removeAttr("tabindex"),p.prop("disabled",!1),p.on("click",function(e){e.preventDefault(),t.expanded.set(!t.expanded.get())}),t.expanded.bind(function(e){p.attr("aria-expanded",String(e)),p.toggleClass("active",e),e?l():(""!==(e=G.state("changesetStatus").get())&&"auto-draft"!==e||(e="publish"),(G.state("selectedChangesetStatus").get()!==e||"future"===G.state("selectedChangesetStatus").get()&&G.state("selectedChangesetDate").get()!==G.state("changesetDate").get())&&c())}),s=new G.Control("changeset_status",{priority:10,type:"radio",section:"publish_settings",setting:G.state("selectedChangesetStatus"),templateId:"customize-selected-changeset-status-control",label:G.l10n.action,choices:G.settings.changeset.statusChoices}),G.control.add(s),(i=new G.DateTimeControl("changeset_scheduled_date",{priority:20,section:"publish_settings",setting:G.state("selectedChangesetDate"),minYear:(new Date).getFullYear(),allowPastDate:!1,includeTime:!0,twelveHourFormat:/a/i.test(G.settings.timeFormat),description:G.l10n.scheduleDescription})).notifications.alt=!0,G.control.add(i),a=function(){G.state("selectedChangesetStatus").set("publish"),G.previewer.save()},s=function(){var e="future"===G.state("changesetStatus").get()&&"future"===G.state("selectedChangesetStatus").get()&&G.state("changesetDate").get()&&G.state("selectedChangesetDate").get()===G.state("changesetDate").get()&&0<=G.utils.getRemainingTime(G.state("changesetDate").get());e&&!o?o=setInterval(function(){var e=G.utils.getRemainingTime(G.state("changesetDate").get());G.state("remainingTimeToPublish").set(e),e<=0&&(clearInterval(o),o=0,a())},1e3):!e&&o&&(clearInterval(o),o=0)},G.state("changesetDate").bind(s),G.state("selectedChangesetDate").bind(s),G.state("changesetStatus").bind(s),G.state("selectedChangesetStatus").bind(s),s(),i.active.validate=function(){return"future"===G.state("selectedChangesetStatus").get()},(s=function(e){i.active.set("future"===e)})(G.state("selectedChangesetStatus").get()),G.state("selectedChangesetStatus").bind(s),G.state("saving").bind(function(e){e&&"future"===G.state("selectedChangesetStatus").get()&&i.toggleFutureDateNotification(!i.isFutureDate())})}),Y("#customize-controls").on("keydown",function(e){var t=13===e.which,n=Y(e.target);t&&(n.is("input:not([type=button])")||n.is("select"))&&e.preventDefault()}),Y(".customize-info").find("> .accordion-section-title .customize-help-toggle").on("click",function(){var e=Y(this).closest(".accordion-section"),t=e.find(".customize-panel-description:first");e.hasClass("cannot-expand")||(e.hasClass("open")?(e.toggleClass("open"),t.slideUp(G.Panel.prototype.defaultExpandedArguments.duration,function(){t.trigger("toggled")}),Y(this).attr("aria-expanded",!1)):(t.slideDown(G.Panel.prototype.defaultExpandedArguments.duration,function(){t.trigger("toggled")}),e.toggleClass("open"),Y(this).attr("aria-expanded",!0)))}),G.previewer=new G.Previewer({container:"#customize-preview",form:"#customize-controls",previewUrl:G.settings.url.preview,allowedUrls:G.settings.url.allowed},{nonce:G.settings.nonce,query:function(e){var t={wp_customize:"on",customize_theme:G.settings.theme.stylesheet,nonce:this.nonce.preview,customize_changeset_uuid:G.settings.changeset.uuid};return!G.settings.changeset.autosaved&&G.state("saved").get()||(t.customize_autosaved="on"),t.customized=JSON.stringify(G.dirtyValues({unsaved:e&&e.excludeCustomizedSaved})),t},save:function(i){var e,t,a=this,o=Y.Deferred(),s=G.state("selectedChangesetStatus").get(),r=G.state("selectedChangesetDate").get(),n=G.state("processing"),c={},l=[],d=[],u=[];function p(e){c[e.id]=!0}return i&&i.status&&(s=i.status),G.state("saving").get()&&(o.reject("already_saving"),o.promise()),G.state("saving").set(!0),t=function(){var n={},t=G._latestRevision,e="client_side_error";if(G.bind("change",p),G.notifications.remove(e),G.each(function(t){t.notifications.each(function(e){"error"!==e.type||e.fromServer||(l.push(t.id),n[t.id]||(n[t.id]={}),n[t.id][e.code]=e)})}),G.control.each(function(t){t.setting&&(t.setting.id||!t.active.get())||t.notifications.each(function(e){"error"===e.type&&u.push([t])})}),d=_.union(u,_.values(G.findControlsForSettings(l))),!_.isEmpty(d))return d[0][0].focus(),G.unbind("change",p),l.length&&G.notifications.add(new G.Notification(e,{message:(1===l.length?G.l10n.saveBlockedError.singular:G.l10n.saveBlockedError.plural).replace(/%s/g,String(l.length)),type:"error",dismissible:!0,saveFailure:!0})),o.rejectWith(a,[{setting_invalidities:n}]),G.state("saving").set(!1),o.promise();e=Y.extend(a.query({excludeCustomizedSaved:!1}),{nonce:a.nonce.save,customize_changeset_status:s}),i&&i.date?e.customize_changeset_date=i.date:"future"===s&&r&&(e.customize_changeset_date=r),i&&i.title&&(e.customize_changeset_title=i.title),G.trigger("save-request-params",e),e=wp.ajax.post("customize_save",e),G.state("processing").set(G.state("processing").get()+1),G.trigger("save",e),e.always(function(){G.state("processing").set(G.state("processing").get()-1),G.state("saving").set(!1),G.unbind("change",p)}),G.notifications.each(function(e){e.saveFailure&&G.notifications.remove(e.code)}),e.fail(function(e){var t,n={type:"error",dismissible:!0,fromServer:!0,saveFailure:!0};"0"===e?e="not_logged_in":"-1"===e&&(e="invalid_nonce"),"invalid_nonce"===e?a.cheatin():"not_logged_in"===e?(a.preview.iframe.hide(),a.login().done(function(){a.save(),a.preview.iframe.show()})):e.code?"not_future_date"===e.code&&G.section.has("publish_settings")&&G.section("publish_settings").active.get()&&G.control.has("changeset_scheduled_date")?G.control("changeset_scheduled_date").toggleFutureDateNotification(!0).focus():"changeset_locked"!==e.code&&(t=new G.Notification(e.code,_.extend(n,{message:e.message}))):t=new G.Notification("unknown_error",_.extend(n,{message:G.l10n.unknownRequestFail})),t&&G.notifications.add(t),e.setting_validities&&G._handleSettingValidities({settingValidities:e.setting_validities,focusInvalidControl:!0}),o.rejectWith(a,[e]),G.trigger("error",e),"changeset_already_published"===e.code&&e.next_changeset_uuid&&(G.settings.changeset.uuid=e.next_changeset_uuid,G.state("changesetStatus").set(""),G.settings.changeset.branching&&h.send("changeset-uuid",G.settings.changeset.uuid),G.previewer.send("changeset-uuid",G.settings.changeset.uuid))}),e.done(function(e){a.send("saved",e),G.state("changesetStatus").set(e.changeset_status),e.changeset_date&&G.state("changesetDate").set(e.changeset_date),"publish"===e.changeset_status&&(G.each(function(e){e._dirty&&(_.isUndefined(G._latestSettingRevisions[e.id])||G._latestSettingRevisions[e.id]<=t)&&(e._dirty=!1)}),G.state("changesetStatus").set(""),G.settings.changeset.uuid=e.next_changeset_uuid,G.settings.changeset.branching&&h.send("changeset-uuid",G.settings.changeset.uuid)),G._lastSavedRevision=Math.max(t,G._lastSavedRevision),e.setting_validities&&G._handleSettingValidities({settingValidities:e.setting_validities,focusInvalidControl:!0}),o.resolveWith(a,[e]),G.trigger("saved",e),_.isEmpty(c)||G.state("saved").set(!1)})},0===n()?t():(e=function(){0===n()&&(G.state.unbind("change",e),t())},G.state.bind("change",e)),o.promise()},trash:function(){var e,n,i;G.state("trashing").set(!0),G.state("processing").set(G.state("processing").get()+1),e=wp.ajax.post("customize_trash",{customize_changeset_uuid:G.settings.changeset.uuid,nonce:G.settings.nonce.trash}),G.notifications.add(new G.OverlayNotification("changeset_trashing",{type:"info",message:G.l10n.revertingChanges,loading:!0})),n=function(){var e,t=document.createElement("a");G.state("changesetStatus").set("trash"),G.each(function(e){e._dirty=!1}),G.state("saved").set(!0),t.href=location.href,delete(e=G.utils.parseQueryString(t.search.substr(1))).changeset_uuid,e.return=G.settings.url.return,t.search=Y.param(e),location.replace(t.href)},i=function(e,t){e=e||"unknown_error";G.state("processing").set(G.state("processing").get()-1),G.state("trashing").set(!1),G.notifications.remove("changeset_trashing"),G.notifications.add(new G.Notification(e,{message:t||G.l10n.unknownError,dismissible:!0,type:"error"}))},e.done(function(e){n(e.message)}),e.fail(function(e){var t=e.code||"trashing_failed";e.success||"non_existent_changeset"===t||"changeset_already_trashed"===t?n(e.message):i(t,e.message)})},getFrontendPreviewUrl:function(){var e,t=document.createElement("a");return t.href=this.previewUrl.get(),e=G.utils.parseQueryString(t.search.substr(1)),G.state("changesetStatus").get()&&"publish"!==G.state("changesetStatus").get()&&(e.customize_changeset_uuid=G.settings.changeset.uuid),G.state("activated").get()||(e.customize_theme=G.settings.theme.stylesheet),t.search=Y.param(e),t.href}}),Y.ajaxPrefilter(function(e){/wp_customize=on/.test(e.data)&&(e.data+="&"+Y.param({customize_preview_nonce:G.settings.nonce.preview}))}),G.previewer.bind("nonce",function(e){Y.extend(this.nonce,e)}),G.bind("nonce-refresh",function(e){Y.extend(G.settings.nonce,e),Y.extend(G.previewer.nonce,e),G.previewer.send("nonce-refresh",e)}),Y.each(G.settings.settings,function(e,t){var n=G.settingConstructor[t.type]||G.Setting;G.add(new n(e,t.value,{transport:t.transport,previewer:G.previewer,dirty:!!t.dirty}))}),Y.each(G.settings.panels,function(e,t){var n=G.panelConstructor[t.type]||G.Panel,t=_.extend({params:t},t);G.panel.add(new n(e,t))}),Y.each(G.settings.sections,function(e,t){var n=G.sectionConstructor[t.type]||G.Section,t=_.extend({params:t},t);G.section.add(new n(e,t))}),Y.each(G.settings.controls,function(e,t){var n=G.controlConstructor[t.type]||G.Control,t=_.extend({params:t},t);G.control.add(new n(e,t))}),_.each(["panel","section","control"],function(e){var t=G.settings.autofocus[e];t&&G[e](t,function(e){e.deferred.embedded.done(function(){G.previewer.deferred.active.done(function(){e.focus()})})})}),G.bind("ready",G.reflowPaneContents),Y([G.panel,G.section,G.control]).each(function(e,t){var n=_.debounce(G.reflowPaneContents,G.settings.timeouts.reflowPaneContents);t.bind("add",n),t.bind("change",n),t.bind("remove",n)}),G.bind("ready",function(){var e,t,n;G.notifications.container=Y("#customize-notifications-area"),G.notifications.bind("change",_.debounce(function(){G.notifications.render()})),e=Y(".wp-full-overlay-sidebar-content"),G.notifications.bind("rendered",function(){e.css("top",""),0!==G.notifications.count()&&(t=G.notifications.container.outerHeight()+1,n=parseInt(e.css("top"),10),e.css("top",n+t+"px")),G.notifications.trigger("sidebarTopUpdated")}),G.notifications.render()}),s=G.state,c=s.instance("saved"),l=s.instance("saving"),f=s.instance("trashing"),m=s.instance("activated"),g=s.instance("processing"),v=s.instance("paneVisible"),w=s.instance("expandedPanel"),b=s.instance("expandedSection"),C=s.instance("changesetStatus"),y=s.instance("selectedChangesetStatus"),x=s.instance("changesetDate"),k=s.instance("selectedChangesetDate"),z=s.instance("previewerAlive"),I=s.instance("editShortcutVisibility"),S=s.instance("changesetLocked"),s.bind("change",function(){var e;m()?""===C.get()&&c()?(G.settings.changeset.currentUserCanPublish?d.val(G.l10n.published):d.val(G.l10n.saved),i.find(".screen-reader-text").text(G.l10n.close)):("draft"===y()?c()&&y()===C()?d.val(G.l10n.draftSaved):d.val(G.l10n.saveDraft):"future"===y()?!c()||y()!==C()||x.get()!==k.get()?d.val(G.l10n.schedule):d.val(G.l10n.scheduled):G.settings.changeset.currentUserCanPublish&&d.val(G.l10n.publish),i.find(".screen-reader-text").text(G.l10n.cancel)):(d.val(G.l10n.activate),i.find(".screen-reader-text").text(G.l10n.cancel)),e=!l()&&!f()&&!S()&&(!m()||!c()||C()!==y()&&""!==C()||"future"===y()&&x.get()!==k.get()),d.prop("disabled",!e)}),y.validate=function(e){return""===e||"auto-draft"===e?null:e},e=G.settings.changeset.currentUserCanPublish?"publish":"draft",C(G.settings.changeset.status),S(Boolean(G.settings.changeset.lockUser)),x(G.settings.changeset.publishDate),k(G.settings.changeset.publishDate),y(""===G.settings.changeset.status||"auto-draft"===G.settings.changeset.status?e:G.settings.changeset.status),y.link(C),c(!0),""===C()&&G.each(function(e){e._dirty&&c(!1)}),l(!1),m(G.settings.theme.active),g(0),v(!0),w(!1),b(!1),z(!0),I("visible"),G.bind("change",function(){s("saved").get()&&s("saved").set(!1)}),G.settings.changeset.branching&&c.bind(function(e){e||r(!0)}),l.bind(function(e){o.toggleClass("saving",e)}),f.bind(function(e){o.toggleClass("trashing",e)}),G.bind("saved",function(e){s("saved").set(!0),"publish"===e.changeset_status&&s("activated").set(!0)}),m.bind(function(e){e&&G.trigger("activated")}),r=function(e){var t,n;if(history.replaceState){if((t=document.createElement("a")).href=location.href,n=G.utils.parseQueryString(t.search.substr(1)),e){if(n.changeset_uuid===G.settings.changeset.uuid)return;n.changeset_uuid=G.settings.changeset.uuid}else{if(!n.changeset_uuid)return;delete n.changeset_uuid}t.search=Y.param(n),history.replaceState({},document.title,t.href)}},G.settings.changeset.branching&&C.bind(function(e){r(""!==e&&"publish"!==e&&"trash"!==e)}),T=G.OverlayNotification.extend({templateId:"customize-changeset-locked-notification",lockUser:null,initialize:function(e,t){e=e||"changeset_locked",t=_.extend({message:"",type:"warning",containerClasses:"",lockUser:{}},t);t.containerClasses+=" notification-changeset-locked",G.OverlayNotification.prototype.initialize.call(this,e,t)},render:function(){var t,n,i=this,e=_.extend({allowOverride:!1,returnUrl:G.settings.url.return,previewUrl:G.previewer.previewUrl.get(),frontendPreviewUrl:G.previewer.getFrontendPreviewUrl()},this),a=G.OverlayNotification.prototype.render.call(e);return G.requestChangesetUpdate({},{autosave:!0}).fail(function(e){e.autosaved||a.find(".notice-error").prop("hidden",!1).text(e.message||G.l10n.unknownRequestFail)}),(t=a.find(".customize-notice-take-over-button")).on("click",function(e){e.preventDefault(),n||(t.addClass("disabled"),(n=wp.ajax.post("customize_override_changeset_lock",{wp_customize:"on",customize_theme:G.settings.theme.stylesheet,customize_changeset_uuid:G.settings.changeset.uuid,nonce:G.settings.nonce.override_lock})).done(function(){G.notifications.remove(i.code),G.state("changesetLocked").set(!1)}),n.fail(function(e){e=e.message||G.l10n.unknownRequestFail;a.find(".notice-error").prop("hidden",!1).text(e),n.always(function(){t.removeClass("disabled")})}),n.always(function(){n=null}))}),a}}),G.settings.changeset.lockUser&&W({allowOverride:!0}),Y(document).on("heartbeat-send.update_lock_notice",function(e,t){t.check_changeset_lock=!0,t.changeset_uuid=G.settings.changeset.uuid}),Y(document).on("heartbeat-tick.update_lock_notice",function(e,t){var n,i="changeset_locked";t.customize_changeset_lock_user&&((n=G.notifications(i))&&n.lockUser.id!==G.settings.changeset.lockUser.id&&G.notifications.remove(i),W({lockUser:t.customize_changeset_lock_user}))}),G.bind("error",function(e){"changeset_locked"===e.code&&e.lock_user&&W({lockUser:e.lock_user})}),U=!(I=[]),G.settings.changeset.autosaved&&(G.state("saved").set(!1),I.push("customize_autosaved")),G.settings.changeset.branching||G.settings.changeset.status&&"auto-draft"!==G.settings.changeset.status||I.push("changeset_uuid"),0<I.length&&(I=I,j=document.createElement("a"),D=0,j.href=location.href,E=G.utils.parseQueryString(j.search.substr(1)),_.each(I,function(e){void 0!==E[e]&&(D+=1,delete E[e])}),0!==D&&(j.search=Y.param(E),history.replaceState({},document.title,j.href))),(G.settings.changeset.latestAutoDraftUuid||G.settings.changeset.hasAutosaveRevision)&&(N="autosave_available",G.notifications.add(new G.Notification(N,{message:G.l10n.autosaveNotice,type:"warning",dismissible:!0,render:function(){var e=G.Notification.prototype.render.call(this),t=e.find("a");return t.prop("href",q()),t.on("click",function(e){e.preventDefault(),location.replace(q())}),e.find(".notice-dismiss").on("click",Q),e}})),P=function(){Q(),G.notifications.remove(N),G.unbind("change",P),G.state("changesetStatus").unbind(P)},G.bind("change",P),G.state("changesetStatus").bind(P)),G.previewer.previewUrl()?G.previewer.refresh():G.previewer.previewUrl(G.settings.url.home),d.on("click",function(e){G.previewer.save(),e.preventDefault()}).on("keydown",function(e){9!==e.which&&(13===e.which&&G.previewer.save(),e.preventDefault())}),i.on("keydown",function(e){9!==e.which&&(13===e.which&&this.click(),e.preventDefault())}),Y(".collapse-sidebar").on("click",function(){G.state("paneVisible").set(!G.state("paneVisible").get())}),G.state("paneVisible").bind(function(e){t.toggleClass("preview-only",!e),t.toggleClass("expanded",e),t.toggleClass("collapsed",!e),e?Y(".collapse-sidebar").attr({"aria-expanded":"true","aria-label":G.l10n.collapseSidebar}):Y(".collapse-sidebar").attr({"aria-expanded":"false","aria-label":G.l10n.expandSidebar})}),o.on("keydown",function(e){var t,n=[],i=[],a=[];27===e.which&&(Y(e.target).is("body")||Y.contains(Y("#customize-controls")[0],e.target))&&(G.control.each(function(e){e.expanded&&e.expanded()&&_.isFunction(e.collapse)&&n.push(e)}),G.section.each(function(e){e.expanded()&&i.push(e)}),G.panel.each(function(e){e.expanded()&&a.push(e)}),0<n.length&&0===i.length&&(n.length=0),(t=n[0]||i[0]||a[0])&&("themes"!==t.params.type?(t.collapse(),e.preventDefault()):o.hasClass("modal-open")?t.closeDetails():G.panel.has("themes")&&G.panel("themes").collapse()))}),Y(".customize-controls-preview-toggle").on("click",function(){G.state("paneVisible").set(!G.state("paneVisible").get())}),O=Y(".wp-full-overlay-sidebar-content"),j=function(e){var t=e,n=G.state("expandedSection").get(),e=G.state("expandedPanel").get();if(L&&L.element&&(F(L.element),L.element.find(".description").off("toggled",A)),!t)if(!n&&e&&e.contentContainer)t=e;else{if(e||!n||!n.contentContainer)return void(L=!1);t=n}(e=t.contentContainer.find(".customize-section-title, .panel-meta").first()).length?((L={instance:t,element:e,parent:e.closest(".customize-pane-child"),height:e.outerHeight()}).element.find(".description").on("toggled",A),n&&V(L.element,L.parent)):L=!1},G.state("expandedSection").bind(j),G.state("expandedPanel").bind(j),O.on("scroll",_.throttle(function(){var e,t;L&&(e=O.scrollTop(),t=M?e===M?0:M<e?1:-1:1,M=e,0!==t&&H(L,e,t))},8)),G.notifications.bind("sidebarTopUpdated",function(){L&&L.element.hasClass("is-sticky")&&L.element.css("top",O.css("top"))}),F=function(e){e.hasClass("is-sticky")&&e.removeClass("is-sticky").addClass("maybe-sticky is-in-view").css("top",O.scrollTop()+"px")},V=function(e,t){e.hasClass("is-in-view")&&(e.removeClass("maybe-sticky is-in-view").css({width:"",top:""}),t.css("padding-top",""))},A=function(){L.height=L.element.outerHeight()},H=function(e,t,n){var i=e.element,a=e.parent,o=e.height,s=parseInt(i.css("top"),10),r=i.hasClass("maybe-sticky"),c=i.hasClass("is-sticky"),e=i.hasClass("is-in-view");if(!(-1===n))return c&&(s=t,i.removeClass("is-sticky").css({top:s+"px",width:""})),void(e&&s+o<t&&(i.removeClass("is-in-view"),a.css("padding-top","")));if(!r&&o<=t)r=!0,i.addClass("maybe-sticky");else if(0===t)return i.removeClass("maybe-sticky is-in-view is-sticky").css({top:"",width:""}),void a.css("padding-top","");e&&!c?t<=s&&i.addClass("is-sticky").css({top:O.css("top"),width:a.outerWidth()+"px"}):r&&!e&&(i.addClass("is-in-view").css("top",t-o+"px"),a.css("padding-top",o+"px"))},G.previewedDevice=G.state("previewedDevice"),G.bind("ready",function(){_.find(G.settings.previewableDevices,function(e,t){if(!0===e.default)return G.previewedDevice.set(t),!0})}),a.find(".devices button").on("click",function(e){G.previewedDevice.set(Y(e.currentTarget).data("device"))}),G.previewedDevice.bind(function(e){var t=Y(".wp-full-overlay"),n="";a.find(".devices button").removeClass("active").attr("aria-pressed",!1),a.find(".devices .preview-"+e).addClass("active").attr("aria-pressed",!0),Y.each(G.settings.previewableDevices,function(e){n+=" preview-"+e}),t.removeClass(n).addClass("preview-"+e)}),n.length&&G("blogname",function(t){function e(){var e=t()||"";n.text(e.toString().trim()||G.l10n.untitledBlogName)}t.bind(e),e()}),h=new G.Messenger({url:G.settings.url.parent,channel:"loader"}),R=!1,h.bind("back",function(){R=!0}),G.bind("change",$),G.state("selectedChangesetStatus").bind($),G.state("selectedChangesetDate").bind($),h.bind("confirm-close",function(){J().done(function(){h.send("confirmed-close",!0)}).fail(function(){h.send("confirmed-close",!1)})}),i.on("click.customize-controls-close",function(e){e.preventDefault(),R?h.send("close"):J().done(function(){Y(window).off("beforeunload.customize-confirm"),window.location.href=i.prop("href")})}),Y.each(["saved","change"],function(e,t){G.bind(t,function(){h.send(t)})}),G.bind("title",function(e){h.send("title",e)}),G.settings.changeset.branching&&h.send("changeset-uuid",G.settings.changeset.uuid),h.send("ready"),Y.each({background_image:{controls:["background_preset","background_position","background_size","background_repeat","background_attachment"],callback:function(e){return!!e}},show_on_front:{controls:["page_on_front","page_for_posts"],callback:function(e){return"page"===e}},header_textcolor:{controls:["header_textcolor"],callback:function(e){return"blank"!==e}}},function(e,i){G(e,function(n){Y.each(i.controls,function(e,t){G.control(t,function(t){function e(e){t.container.toggle(i.callback(e))}e(n.get()),n.bind(e)})})})}),G.control("background_preset",function(e){var i={default:[!1,!1,!1,!1],fill:[!0,!1,!1,!1],fit:[!0,!1,!0,!1],repeat:[!0,!1,!1,!0],custom:[!0,!0,!0,!0]},a={default:[_wpCustomizeBackground.defaults["default-position-x"],_wpCustomizeBackground.defaults["default-position-y"],_wpCustomizeBackground.defaults["default-size"],_wpCustomizeBackground.defaults["default-repeat"],_wpCustomizeBackground.defaults["default-attachment"]],fill:["left","top","cover","no-repeat","fixed"],fit:["left","top","contain","no-repeat","fixed"],repeat:["left","top","auto","repeat","scroll"]},t=function(n){_.each(["background_position","background_size","background_repeat","background_attachment"],function(e,t){e=G.control(e);e&&e.container.toggle(i[n][t])})},n=function(n){_.each(["background_position_x","background_position_y","background_size","background_repeat","background_attachment"],function(e,t){e=G(e);e&&e.set(a[n][t])})},o=e.setting.get();t(o),e.setting.bind("change",function(e){t(e),"custom"!==e&&n(e)})}),G.control("background_repeat",function(t){t.elements[0].unsync(G("background_repeat")),t.element=new G.Element(t.container.find("input")),t.element.set("no-repeat"!==t.setting()),t.element.bind(function(e){t.setting.set(e?"repeat":"no-repeat")}),t.setting.bind(function(e){t.element.set("no-repeat"!==e)})}),G.control("background_attachment",function(t){t.elements[0].unsync(G("background_attachment")),t.element=new G.Element(t.container.find("input")),t.element.set("fixed"!==t.setting()),t.element.bind(function(e){t.setting.set(e?"scroll":"fixed")}),t.setting.bind(function(e){t.element.set("fixed"!==e)})}),G.control("display_header_text",function(t){var n="";t.elements[0].unsync(G("header_textcolor")),t.element=new G.Element(t.container.find("input")),t.element.set("blank"!==t.setting()),t.element.bind(function(e){e||(n=G("header_textcolor").get()),t.setting.set(e?n:"blank")}),t.setting.bind(function(e){t.element.set("blank"!==e)})}),G("show_on_front","page_on_front","page_for_posts",function(i,a,o){function e(){var e="show_on_front_page_collision",t=parseInt(a(),10),n=parseInt(o(),10);"page"===i()&&(this===a&&0<t&&G.previewer.previewUrl.set(G.settings.url.home),this===o&&0<n&&G.previewer.previewUrl.set(G.settings.url.home+"?page_id="+n)),"page"===i()&&t&&n&&t===n?i.notifications.add(new G.Notification(e,{type:"error",message:G.l10n.pageOnFrontError})):i.notifications.remove(e)}i.bind(e),a.bind(e),o.bind(e),e.call(i,i()),G.control("show_on_front",function(e){e.deferred.embedded.done(function(){e.container.append(e.getNotificationsContainerElement())})})}),B=Y.Deferred(),G.section("custom_css",function(t){t.deferred.embedded.done(function(){t.expanded()?B.resolve(t):t.expanded.bind(function(e){e&&B.resolve(t)})})}),B.done(function(e){var t=G.control("custom_css");t.container.find(".customize-control-title:first").addClass("screen-reader-text"),e.container.find(".section-description-buttons .section-description-close").on("click",function(){e.container.find(".section-meta .customize-section-description:first").removeClass("open").slideUp(),e.container.find(".customize-help-toggle").attr("aria-expanded","false").focus()}),t&&!t.setting.get()&&(e.container.find(".section-meta .customize-section-description:first").addClass("open").show().trigger("toggled"),e.container.find(".customize-help-toggle").attr("aria-expanded","true"))}),G.control("header_video",function(n){n.deferred.embedded.done(function(){function e(){var e=G.section(n.section()),t="video_header_not_available";e&&(n.active.get()?e.notifications.remove(t):e.notifications.add(new G.Notification(t,{type:"info",message:G.l10n.videoHeaderNotice})))}e(),n.active.bind(e)})}),G.previewer.bind("selective-refresh-setting-validities",function(e){G._handleSettingValidities({settingValidities:e,focusInvalidControl:!1})}),G.previewer.bind("focus-control-for-setting",function(n){var i=[];G.control.each(function(e){var t=_.pluck(e.settings,"id");-1!==_.indexOf(t,n)&&i.push(e)}),i.length&&(i.sort(function(e,t){return e.priority()-t.priority()}),i[0].focus())}),G.previewer.bind("refresh",function(){G.previewer.refresh()}),G.state("paneVisible").bind(function(e){var t=window.matchMedia?window.matchMedia("screen and ( max-width: 640px )").matches:Y(window).width()<=640;G.state("editShortcutVisibility").set(e||t?"visible":"hidden")}),window.matchMedia&&window.matchMedia("screen and ( max-width: 640px )").addListener(function(){var e=G.state("paneVisible");e.callbacks.fireWith(e,[e.get(),e.get()])}),G.previewer.bind("edit-shortcut-visibility",function(e){G.state("editShortcutVisibility").set(e)}),G.state("editShortcutVisibility").bind(function(e){G.previewer.send("edit-shortcut-visibility",e)}),G.bind("change",function e(){var t,n,i,a=!1;function o(e){e||G.settings.changeset.autosaved||(G.settings.changeset.autosaved=!0,G.previewer.send("autosaving"))}G.unbind("change",e),G.state("saved").bind(o),o(G.state("saved").get()),n=function(){a||(a=!0,G.requestChangesetUpdate({},{autosave:!0}).always(function(){a=!1})),i()},(i=function(){clearTimeout(t),t=setTimeout(function(){n()},G.settings.timeouts.changesetAutoSave)})(),Y(document).on("visibilitychange.wp-customize-changeset-update",function(){document.hidden&&n()}),Y(window).on("beforeunload.wp-customize-changeset-update",function(){n()})}),Y(document).one("tinymce-editor-setup",function(){window.tinymce.ui.FloatPanel&&(!window.tinymce.ui.FloatPanel.zIndex||window.tinymce.ui.FloatPanel.zIndex<500001)&&(window.tinymce.ui.FloatPanel.zIndex=500001)}),o.addClass("ready"),G.trigger("ready")))})}((wp,jQuery));